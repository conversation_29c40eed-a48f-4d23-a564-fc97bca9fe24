import argparse
from pymongo import MongoClient
from tqdm import tqdm
from uq_methods.implementations.num_sets import NumSetsUQ
import re

def split_twitter_response(response, prompt_type):
    # 统一换行符
    response = response.replace('\r\n', '\n').replace('\r', '\n')
    if prompt_type == "sentiment":
        return response.strip(), ""
    elif prompt_type in ("sentiment_reason", "sentiment_reason_first"):
        # 提取 [Label]: ... 和 [Reasoning]: ...，允许顺序任意，reasoning支持多行
        label_match = re.search(r"\[Label\]:\s*([^\n]+)", response)
        reasoning_match = re.search(r"\[Reasoning\]:\s*((?:.|\n)*?)(?=\n\[|$)", response)
        label = label_match.group(1).strip() if label_match else ""
        reasoning = reasoning_match.group(1).strip() if reasoning_match else ""
        return label, reasoning
    else:
        return response.strip(), ""

def main():
    parser = argparse.ArgumentParser(description="NumSets UQ for Twitter Sentiment Analysis")
    parser.add_argument('--mongo_host', type=str, default='localhost')
    parser.add_argument('--mongo_port', type=int, default=27017)
    parser.add_argument('--mongo_db', type=str, default='twitter_sentiment')
    parser.add_argument('--mongo_collection', type=str, default='responses')
    parser.add_argument('--result_collection', type=str, default=None)
    parser.add_argument('--nli_model', type=str, choices=['microsoft/deberta-large-mnli', 'cross-encoder/nli-deberta-v3-base','potsawee/deberta-v3-large-mnli'], 
                       default='potsawee/deberta-v3-large-mnli', 
                       help='NLI model to use for uncertainty quantification')
    args = parser.parse_args()

    mongo_client = MongoClient(host=args.mongo_host, port=args.mongo_port)
    db = mongo_client[args.mongo_db]
    col = db[args.mongo_collection]
    
    # 根据NLI模型自动生成collection名称
    if args.result_collection is None:
        model_name_clean = args.nli_model.replace('/', '_').replace('-', '_')
        args.result_collection = f'twitter_numsets_results_{model_name_clean}'
    
    result_col = db[args.result_collection]

    # 按 tweet_index + prompt_type 分组
    pipeline = [
        {
            "$group": {
                "_id": {"tweet_index": "$tweet_index", "prompt_type": "$prompt_type"},
                "responses": {"$push": "$response_text"},
                "meta": {"$first": {
                    "tweet_index": "$tweet_index",
                    "prompt_type": "$prompt_type",
                    "text": "$text",
                    "validation": "$validation"
                }}
            }
        }
    ]
    groups = list(col.aggregate(pipeline))
    # 使用指定的NLI模型 (microsoft/deberta-large-mnli作为默认，deberta-v3作为可选项)
    model_name = args.nli_model
    uq = NumSetsUQ(model_name)

    for group in tqdm(groups, desc="UQ分析分组"):
        responses = [r for r in group['responses'] if r]
        if len(responses) < 2:
            continue
        prompt_type = group['_id']['prompt_type']
        tweet_index = group['_id']['tweet_index']
        # 检查是否已存在且完整
        existing = result_col.find_one({
            "tweet_index": tweet_index,
            "prompt_type": prompt_type
        })
        skip = False
        if existing:
            # 检查responses数量一致，label_uq_result和reasoning_uq_result都存在且有内容
            if (
                'responses' in existing and len(existing['responses']) == len(responses)
                and 'label_uq_result' in existing and existing['label_uq_result']
                and 'reasoning_uq_result' in existing and existing['reasoning_uq_result']
            ):
                skip = True
        if skip:
            print(f"跳过: tweet_index={tweet_index}, prompt_type={prompt_type} (已存在且完整)")
            continue
        labels, reasonings = [], []
        for resp in responses:
            label, reasoning = split_twitter_response(resp, prompt_type)
            if label: labels.append(label)
            if reasoning: reasonings.append(reasoning)
        # 对label做UQ
        label_uq_result = uq.compute_uncertainty(labels) if labels else {}
        # 对reasoning做UQ（仅有内容时）
        reasoning_uq_result = uq.compute_uncertainty(reasonings) if reasonings else {}
        save_doc = {
            "tweet_index": tweet_index,
            "prompt_type": prompt_type,
            "responses": responses,
            "labels": labels,
            "reasonings": reasonings,
            "label_uq_result": label_uq_result,
            "reasoning_uq_result": reasoning_uq_result,
            "meta": group["meta"],
            "nli_model": model_name,
            "uq_method": "NumSets",
            "uq_method_description": "基于NLI的NumSets不确定性度量方法，通过两两NLI推理构建连通分量，计算集合熵"
        }
        result_col.replace_one({"tweet_index": tweet_index, "prompt_type": prompt_type}, save_doc, upsert=True)
        print(f"已保存: tweet_index={tweet_index}, prompt_type={prompt_type}")

    print("全部分析完成！")

if __name__ == "__main__":
    main() 