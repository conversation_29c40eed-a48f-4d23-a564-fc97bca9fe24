#!/usr/bin/env python3
"""
Test script for unlimited speed MongoDB-optimized Qwen client
"""
import asyncio
import os
from core.mongodb_optimized_client import MongoDBOptimizedClient

async def test_unlimited_speed():
    """Test the unlimited speed mode"""
    
    client = MongoDBOptimizedClient(
        api_key=os.getenv("DASHSCOPE_API_KEY"),
        max_concurrent=50  # High concurrency for 500 queries
    )
    
    # Test commit data
    test_commit = {
        "sha": "test1234567890abcdef1234567890abcdef12",
        "repo_name": "test-repo",
        "author": "test-author",
        "date": "2024-01-01",
        "message": "Test commit message"
    }
    
    # Simulate 30 prompts
    test_prompts = [f"Test prompt {i} for commit analysis" for i in range(30)]
    
    print("🚀 Testing unlimited speed mode...")
    print("⚡ No rate limiting - maximum API throughput")
    print("📊 Real-time progress display enabled")
    
    # Test resume functionality
    resume_info = client.get_resume_info(test_commit["sha"], "single_word")
    
    if resume_info["skip"]:
        print("✅ Skipping - already complete")
    else:
        print(f"🚀 Starting from index {resume_info['start_index']}")
        
        # Process all prompts at maximum speed
        results = await client.batch_generate_for_commit(
            commit_data=test_commit,
            prompts=test_prompts,
            prompt_type="single_word"
        )
        
        print(f"✅ Processed {len(results)} responses at maximum speed")
    
    await client.close()

if __name__ == "__main__":
    asyncio.run(test_unlimited_speed())