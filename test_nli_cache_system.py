#!/usr/bin/env python3
"""
测试新的NLI缓存系统
"""

import os
import sys
import pandas as pd
import tempfile
import shutil

# 添加当前目录到路径
sys.path.append('.')

def test_cache_functions():
    """测试缓存功能"""
    print("Testing NLI cache system...")
    
    try:
        from analyze_all_uq_methods import (
            create_directories, load_nli_csv_cache, save_nli_csv_cache,
            get_text_hash, NLI_CSV_CACHE, OUTPUT_DIR, NLI_CACHE_DIR
        )
        
        # 测试目录创建
        print("1. Testing directory creation...")
        create_directories()
        print(f"   Output directory exists: {os.path.exists(OUTPUT_DIR)}")
        print(f"   Cache directory exists: {os.path.exists(NLI_CACHE_DIR)}")
        
        # 测试哈希生成
        print("\n2. Testing hash generation...")
        text1 = "This is a test sentence."
        text2 = "This is another test sentence."
        hash1 = get_text_hash(text1)
        hash2 = get_text_hash(text2)
        print(f"   Text1 hash: {hash1}")
        print(f"   Text2 hash: {hash2}")
        print(f"   Hashes are different: {hash1 != hash2}")
        
        # 测试缓存操作
        print("\n3. Testing cache operations...")
        
        # 清空缓存进行测试
        NLI_CSV_CACHE.clear()
        
        # 添加测试数据
        import time
        test_data = {
            f"{hash1}||{hash2}||test_model": {
                'text1': text1,
                'text2': text2,
                'model_name': 'test_model',
                'similarity_score': 0.85,
                'text1_hash': hash1,
                'text2_hash': hash2,
                'timestamp': time.strftime("%Y-%m-%d %H:%M:%S")
            }
        }
        
        NLI_CSV_CACHE.update(test_data)
        print(f"   Added test data to cache: {len(NLI_CSV_CACHE)} entries")
        
        # 测试保存
        save_nli_csv_cache()
        print("   Cache saved successfully")
        
        # 清空内存缓存并重新加载
        NLI_CSV_CACHE.clear()
        load_nli_csv_cache()
        print(f"   Cache reloaded: {len(NLI_CSV_CACHE)} entries")
        
        # 验证数据
        key = f"{hash1}||{hash2}||test_model"
        if key in NLI_CSV_CACHE:
            cached_data = NLI_CSV_CACHE[key]
            print(f"   Cached similarity score: {cached_data['similarity_score']}")
            print("   ✓ Cache system working correctly!")
        else:
            print("   ✗ Cache data not found after reload")
        
        return True
        
    except ImportError as e:
        print(f"Import error: {e}")
        return False
    except Exception as e:
        print(f"Error: {e}")
        return False

def test_cache_manager():
    """测试缓存管理器"""
    print("\n4. Testing cache manager...")
    
    try:
        from nli_cache_manager import load_nli_cache, get_text_hash, get_similarity_for_texts
        
        # 测试加载
        df = load_nli_cache()
        if not df.empty:
            print(f"   Cache manager loaded {len(df)} entries")
            print(f"   Columns: {list(df.columns)}")
            
            # 测试查询功能
            if len(df) > 0:
                first_row = df.iloc[0]
                similarity = get_similarity_for_texts(
                    first_row['text1'],
                    first_row['text2'], 
                    first_row['model_name']
                )
                if similarity is not None:
                    print(f"   ✓ Query function working: {similarity}")
                else:
                    print("   ✗ Query function failed")
        else:
            print("   No cache data found (this is expected if cache is empty)")
        
        return True
        
    except Exception as e:
        print(f"   Error testing cache manager: {e}")
        return False

def main():
    print("=" * 50)
    print("NLI Cache System Test")
    print("=" * 50)
    
    # 运行测试
    cache_test = test_cache_functions()
    manager_test = test_cache_manager()
    
    print("\n" + "=" * 50)
    print("Test Results:")
    print(f"Cache functions: {'✓ PASS' if cache_test else '✗ FAIL'}")
    print(f"Cache manager: {'✓ PASS' if manager_test else '✗ FAIL'}")
    
    if cache_test and manager_test:
        print("\n🎉 All tests passed! The NLI cache system is ready to use.")
        print("\nNext steps:")
        print("1. Run: python run_complete_analysis.py --quick_test")
        print("2. Check cache: python nli_cache_manager.py stats")
        print("3. Full analysis: python run_complete_analysis.py")
    else:
        print("\n❌ Some tests failed. Please check the error messages above.")

if __name__ == "__main__":
    main()