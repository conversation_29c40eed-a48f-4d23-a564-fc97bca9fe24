import pandas as pd
import argparse
import re
from typing import List, <PERSON><PERSON>, Dict, Any, NamedTuple
from tqdm import tqdm
import logging
import hashlib
import pickle
import os

# 导入统一的NLI计算器
from core.nli_calculator import NLICalculator, CachedNLICalculator, NLIResult

# 导入UQ方法实现 - NLI版本
from uq_methods.implementations.deg_mat_nli_entail import DegMatNLIEntailUQ
from uq_methods.implementations.eccentricity_nli_entail import EccentricityNLIEntailUQ
from uq_methods.implementations.eig_val_laplacian_nli_entail import EigValLaplacianNLIEntailUQ

# 导入UQ方法实现 - Jaccard版本
from uq_methods.implementations.deg_mat_jaccard import DegMatJaccardUQ
from uq_methods.implementations.eccentricity_jaccard import EccentricityJaccardUQ
from uq_methods.implementations.eig_val_laplacian_jaccard import EigValLaplacianJaccardUQ

# 导入NumSets方法
from uq_methods.implementations.num_sets import NumSetsUQ

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 全局缓存
SIMILARITY_CACHE = {}
NLI_CACHE = {}
NLI_CSV_CACHE = {}  # CSV格式的NLI缓存
CACHE_FILE = "data/similarity_cache.pkl"
NLI_CACHE_DIR = "cache"
NLI_CSV_CACHE_FILE = os.path.join("cache", "nli_results_cache.csv")
OUTPUT_DIR = "data/Uq_Evaluation_20250731"

def create_directories():
    """创建必要的目录结构"""
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    os.makedirs(NLI_CACHE_DIR, exist_ok=True)
    os.makedirs(os.path.dirname(CACHE_FILE), exist_ok=True)
    logger.info(f"Created directories: {OUTPUT_DIR}, {NLI_CACHE_DIR}")

def load_nli_csv_cache():
    """加载CSV格式的NLI缓存"""
    global NLI_CSV_CACHE
    if os.path.exists(NLI_CSV_CACHE_FILE):
        try:
            import pandas as pd
            df = pd.read_csv(NLI_CSV_CACHE_FILE)
            # 将DataFrame转换为字典格式，使用复合键
            NLI_CSV_CACHE = {}
            for _, row in df.iterrows():
                key = f"{row['text1_hash']}||{row['text2_hash']}||{row['model_name']}"
                cache_entry = {
                    'text1': row['text1'],
                    'text2': row['text2'],
                    'model_name': row['model_name'],
                    'text1_hash': row['text1_hash'],
                    'text2_hash': row['text2_hash'],
                    'timestamp': row['timestamp']
                }
                
                # 处理新格式（三个分数）和旧格式（单个similarity_score）的兼容性
                if 'entailment' in row and 'neutral' in row and 'contradiction' in row:
                    # 新格式：包含三个分数
                    cache_entry.update({
                        'entailment': row['entailment'],
                        'neutral': row['neutral'],
                        'contradiction': row['contradiction']
                    })
                elif 'similarity_score' in row:
                    # 旧格式：只有similarity_score，假设是entailment分数
                    cache_entry.update({
                        'entailment': row['similarity_score'],
                        'neutral': 0.33,  # 默认值
                        'contradiction': 0.33  # 默认值
                    })
                else:
                    # 如果都没有，使用均匀分布
                    cache_entry.update({
                        'entailment': 0.33,
                        'neutral': 0.34,
                        'contradiction': 0.33
                    })
                
                NLI_CSV_CACHE[key] = cache_entry
            logger.info(f"Loaded NLI CSV cache with {len(NLI_CSV_CACHE)} entries from {NLI_CSV_CACHE_FILE}")
        except Exception as e:
            logger.warning(f"Failed to load NLI CSV cache: {e}")
            NLI_CSV_CACHE = {}
    else:
        NLI_CSV_CACHE = {}

def save_nli_csv_cache():
    """保存CSV格式的NLI缓存"""
    try:
        import pandas as pd
        import time
        
        if not NLI_CSV_CACHE:
            return
        
        # 转换字典为DataFrame
        rows = []
        for key, data in NLI_CSV_CACHE.items():
            row = {
                'text1': data['text1'],
                'text2': data['text2'],
                'model_name': data['model_name'],
                'text1_hash': data['text1_hash'],
                'text2_hash': data['text2_hash'],
                'timestamp': data['timestamp']
            }
            
            # 添加三个NLI分数
            if 'entailment' in data and 'neutral' in data and 'contradiction' in data:
                row.update({
                    'entailment': data['entailment'],
                    'neutral': data['neutral'],
                    'contradiction': data['contradiction']
                })
            elif 'similarity_score' in data:
                # 向后兼容：将similarity_score作为entailment分数
                row.update({
                    'entailment': data['similarity_score'],
                    'neutral': 0.33,
                    'contradiction': 0.33
                })
            else:
                # 默认均匀分布
                row.update({
                    'entailment': 0.33,
                    'neutral': 0.34,
                    'contradiction': 0.33
                })
            
            rows.append(row)
        
        df = pd.DataFrame(rows)
        os.makedirs(os.path.dirname(NLI_CSV_CACHE_FILE), exist_ok=True)
        df.to_csv(NLI_CSV_CACHE_FILE, index=False, encoding='utf-8-sig')
        logger.info(f"Saved NLI CSV cache with {len(rows)} entries to {NLI_CSV_CACHE_FILE}")
    except Exception as e:
        logger.warning(f"Failed to save NLI CSV cache: {e}")

def save_nli_csv_cache_with_timestamp():
    """带时间戳保存CSV格式的NLI缓存"""
    try:
        import pandas as pd
        import time
        
        if not NLI_CSV_CACHE:
            return
        
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        timestamped_file = os.path.join(NLI_CACHE_DIR, f"nli_results_cache_{timestamp}.csv")
        
        # 转换字典为DataFrame
        rows = []
        for key, data in NLI_CSV_CACHE.items():
            rows.append({
                'text1': data['text1'],
                'text2': data['text2'],
                'model_name': data['model_name'],
                'similarity_score': data['similarity_score'],
                'text1_hash': data['text1_hash'],
                'text2_hash': data['text2_hash'],
                'timestamp': data['timestamp']
            })
        
        df = pd.DataFrame(rows)
        os.makedirs(os.path.dirname(timestamped_file), exist_ok=True)
        df.to_csv(timestamped_file, index=False, encoding='utf-8-sig')
        logger.info(f"Saved timestamped NLI CSV cache with {len(rows)} entries to {timestamped_file}")
    except Exception as e:
        logger.warning(f"Failed to save timestamped NLI CSV cache: {e}")

def load_cache():
    """加载缓存文件"""
    global SIMILARITY_CACHE, NLI_CACHE
    if os.path.exists(CACHE_FILE):
        try:
            with open(CACHE_FILE, 'rb') as f:
                SIMILARITY_CACHE = pickle.load(f)
            logger.info(f"Loaded similarity cache with {len(SIMILARITY_CACHE)} entries")
        except Exception as e:
            logger.warning(f"Failed to load cache: {e}")
            SIMILARITY_CACHE = {}
    else:
        SIMILARITY_CACHE = {}
    
    # 加载NLI缓存
    load_nli_cache()
    # 加载CSV格式的NLI缓存
    load_nli_csv_cache()

def save_cache():
    """保存缓存文件"""
    try:
        os.makedirs(os.path.dirname(CACHE_FILE), exist_ok=True)
        with open(CACHE_FILE, 'wb') as f:
            pickle.dump(SIMILARITY_CACHE, f)
        logger.info(f"Saved similarity cache with {len(SIMILARITY_CACHE)} entries")
    except Exception as e:
        logger.warning(f"Failed to save cache: {e}")
    
    # 保存NLI缓存
    save_nli_cache()
    # 保存CSV格式的NLI缓存
    save_nli_csv_cache()

def load_nli_cache():
    """加载NLI缓存文件"""
    global NLI_CACHE
    nli_cache_file = os.path.join(NLI_CACHE_DIR, "nli_cache.pkl")
    if os.path.exists(nli_cache_file):
        try:
            with open(nli_cache_file, 'rb') as f:
                NLI_CACHE = pickle.load(f)
            logger.info(f"Loaded NLI cache with {len(NLI_CACHE)} entries")
        except Exception as e:
            logger.warning(f"Failed to load NLI cache: {e}")
            NLI_CACHE = {}
    else:
        NLI_CACHE = {}

def save_nli_cache():
    """保存NLI缓存文件"""
    try:
        os.makedirs(NLI_CACHE_DIR, exist_ok=True)
        nli_cache_file = os.path.join(NLI_CACHE_DIR, "nli_cache.pkl")
        with open(nli_cache_file, 'wb') as f:
            pickle.dump(NLI_CACHE, f)
        logger.info(f"Saved NLI cache with {len(NLI_CACHE)} entries to {nli_cache_file}")
    except Exception as e:
        logger.warning(f"Failed to save NLI cache: {e}")

def save_nli_cache_with_timestamp():
    """带时间戳保存NLI缓存文件"""
    try:
        import time
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        os.makedirs(NLI_CACHE_DIR, exist_ok=True)
        nli_cache_file = os.path.join(NLI_CACHE_DIR, f"nli_cache_{timestamp}.pkl")
        with open(nli_cache_file, 'wb') as f:
            pickle.dump(NLI_CACHE, f)
        logger.info(f"Saved timestamped NLI cache with {len(NLI_CACHE)} entries to {nli_cache_file}")
    except Exception as e:
        logger.warning(f"Failed to save timestamped NLI cache: {e}")

def get_cache_key(text1: str, text2: str, method_type: str) -> str:
    """生成缓存键"""
    # 使用哈希来处理长文本，确保键的唯一性
    combined = f"{text1}||{text2}||{method_type}"
    return hashlib.md5(combined.encode()).hexdigest()

def cached_jaccard_similarity(text1: str, text2: str) -> float:
    """带缓存的Jaccard相似度计算"""
    cache_key = get_cache_key(text1, text2, "jaccard")
    
    if cache_key in SIMILARITY_CACHE:
        return SIMILARITY_CACHE[cache_key]
    
    # 计算Jaccard相似度
    words1 = set(text1.lower().split())
    words2 = set(text2.lower().split())
    intersection = len(words1.intersection(words2))
    union = len(words1.union(words2))
    similarity = intersection / union if union > 0 else 0.0
    
    # 缓存结果
    SIMILARITY_CACHE[cache_key] = similarity
    return similarity

def get_text_hash(text: str) -> str:
    """生成文本的哈希值"""
    return hashlib.md5(text.encode('utf-8')).hexdigest()

# 全局NLI计算器
_nli_calculators = {}

def get_nli_calculator(model_name: str) -> CachedNLICalculator:
    """获取NLI计算器实例（单例模式）"""
    if model_name not in _nli_calculators:
        _nli_calculators[model_name] = CachedNLICalculator(model_name, verbose=True)
    return _nli_calculators[model_name]

def cached_nli_scores(text1: str, text2: str, model_name: str) -> NLIResult:
    """带缓存的完整NLI分数计算"""
    # 生成文本哈希和缓存键
    text1_hash = get_text_hash(text1)
    text2_hash = get_text_hash(text2)
    csv_cache_key = f"{text1_hash}||{text2_hash}||{model_name}"
    old_cache_key = get_cache_key(text1, text2, f"nli_{model_name}")
    
    # 首先检查CSV缓存
    if csv_cache_key in NLI_CSV_CACHE:
        cached_data = NLI_CSV_CACHE[csv_cache_key]
        return NLIResult(
            entailment=cached_data['entailment'],
            neutral=cached_data['neutral'],
            contradiction=cached_data['contradiction']
        )
    
    # 然后检查旧的NLI缓存（向后兼容）
    if old_cache_key in NLI_CACHE:
        similarity = NLI_CACHE[old_cache_key]
        # 将结果迁移到CSV缓存，假设similarity是entailment分数
        import time
        NLI_CSV_CACHE[csv_cache_key] = {
            'text1': text1,
            'text2': text2,
            'model_name': model_name,
            'entailment': similarity,
            'neutral': 0.33,
            'contradiction': 0.33,
            'text1_hash': text1_hash,
            'text2_hash': text2_hash,
            'timestamp': time.strftime("%Y-%m-%d %H:%M:%S")
        }
        return NLIResult(entailment=similarity, neutral=0.33, contradiction=0.33)
    
    # 检查通用缓存（向后兼容）
    if old_cache_key in SIMILARITY_CACHE:
        similarity = SIMILARITY_CACHE[old_cache_key]
        # 将结果迁移到CSV缓存
        import time
        NLI_CSV_CACHE[csv_cache_key] = {
            'text1': text1,
            'text2': text2,
            'model_name': model_name,
            'entailment': similarity,
            'neutral': 0.33,
            'contradiction': 0.33,
            'text1_hash': text1_hash,
            'text2_hash': text2_hash,
            'timestamp': time.strftime("%Y-%m-%d %H:%M:%S")
        }
        return NLIResult(entailment=similarity, neutral=0.33, contradiction=0.33)
    
    # 计算新的NLI分数
    logger.info(f"Computing new NLI scores for model {model_name}")
    nli_calculator = get_nli_calculator(model_name)
    nli_result = nli_calculator.compute_nli_scores(text1, text2)
    
    # 保存到CSV缓存
    import time
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
    
    NLI_CSV_CACHE[csv_cache_key] = {
        'text1': text1,
        'text2': text2,
        'model_name': model_name,
        'entailment': nli_result.entailment,
        'neutral': nli_result.neutral,
        'contradiction': nli_result.contradiction,
        'text1_hash': text1_hash,
        'text2_hash': text2_hash,
        'timestamp': timestamp
    }
    
    return nli_result

def cached_nli_similarity(text1: str, text2: str, nli_model) -> float:
    """带缓存的NLI相似度计算（向后兼容，返回entailment分数）"""
    model_name = nli_model.model_name if hasattr(nli_model, 'model_name') else str(nli_model)
    nli_result = cached_nli_scores(text1, text2, model_name)
    return nli_result.entailment

class CachedUQMethods:
    """包装UQ方法，使用缓存的相似度计算"""
    
    def __init__(self, nli_model_name: str = "microsoft/deberta-large-mnli"):
        self.nli_model_name = nli_model_name
        
        # 使用统一的NLI计算器替代单独的模型实例
        self.nli_calculator = get_nli_calculator(nli_model_name)
        
        # 初始化所有UQ方法
        self.methods = {
            # NLI版本 - 使用缓存
            'deg_mat_nli': self._create_cached_nli_method(DegMatNLIEntailUQ, nli_model_name),
            'eccentricity_nli': self._create_cached_nli_method(EccentricityNLIEntailUQ, nli_model_name),
            'eig_val_nli': self._create_cached_nli_method(EigValLaplacianNLIEntailUQ, nli_model_name),
            
            # Jaccard版本 - 使用缓存
            'deg_mat_jaccard': self._create_cached_jaccard_method(DegMatJaccardUQ),
            'eccentricity_jaccard': self._create_cached_jaccard_method(EccentricityJaccardUQ),
            'eig_val_jaccard': self._create_cached_jaccard_method(EigValLaplacianJaccardUQ),
            
            # NumSets方法
            'num_sets': NumSetsUQ(model_name=nli_model_name)
        }
    
    def _create_cached_nli_method(self, method_class, model_name):
        """创建使用缓存NLI计算的方法实例"""
        
        # 创建简化的方法包装器
        class SimplifiedNLIMethod:
            def __init__(self, method_name, model_name):
                self.method_name = method_name
                self.model_name = model_name
                self.verbose = False
            
            def _compute_similarity_matrix(self, responses: List[str]):
                """使用统一NLI计算器计算相似度矩阵"""
                import numpy as np
                n = len(responses)
                W = np.zeros((n, n))
                
                for i in range(n):
                    for j in range(n):
                        if i == j:
                            W[i, j] = 1.0
                        else:
                            # 使用统一的NLI缓存计算
                            nli_ij = cached_nli_scores(responses[i], responses[j], model_name)
                            nli_ji = cached_nli_scores(responses[j], responses[i], model_name)
                            
                            # 使用entailment分数作为相似度
                            score_ij = nli_ij.entailment  
                            score_ji = nli_ji.entailment
                            W[i, j] = (score_ij + score_ji) / 2
                
                W = (W + np.transpose(W)) / 2
                return W
            
            def compute_uncertainty(self, responses: List[str]) -> float:
                """计算不确定性分数"""
                # 计算相似度矩阵
                W = self._compute_similarity_matrix(responses)
                
                # 根据方法类型计算不确定性
                method_name = method_class.__name__
                if "DegMat" in method_name:
                    return self._compute_deg_mat_uncertainty(W)
                elif "Eccentricity" in method_name:
                    return self._compute_eccentricity_uncertainty(W)
                elif "EigVal" in method_name:
                    return self._compute_eigenvalue_uncertainty(W)
                else:
                    # 默认使用度矩阵方法
                    return self._compute_deg_mat_uncertainty(W)
            
            def _compute_deg_mat_uncertainty(self, W):
                """计算度矩阵不确定性"""
                import numpy as np
                degree_matrix = np.diag(np.sum(W, axis=1))
                eigenvalues = np.linalg.eigvals(degree_matrix)
                eigenvalues = eigenvalues[eigenvalues > 1e-10]  # 过滤掉接近0的特征值
                if len(eigenvalues) == 0:
                    return 0.0
                return float(np.max(eigenvalues))
            
            def _compute_eccentricity_uncertainty(self, W):
                """计算偏心度不确定性"""
                import numpy as np
                n = W.shape[0]
                if n <= 1:
                    return 0.0
                
                # 计算每个节点到其他所有节点的最短路径的最大值
                # 这里简化为使用度的倒数作为偏心度的近似
                degrees = np.sum(W, axis=1)
                eccentricities = 1.0 / (degrees + 1e-8)  # 避免除0
                return float(np.max(eccentricities))
            
            def _compute_eigenvalue_uncertainty(self, W):
                """计算拉普拉斯矩阵特征值不确定性"""
                import numpy as np
                n = W.shape[0]
                if n <= 1:
                    return 0.0
                
                # 计算度矩阵
                D = np.diag(np.sum(W, axis=1))
                # 计算拉普拉斯矩阵
                L = D - W
                
                # 计算特征值
                eigenvalues = np.linalg.eigvals(L)
                eigenvalues = np.real(eigenvalues)  # 取实部
                eigenvalues = eigenvalues[eigenvalues > 1e-10]  # 过滤接近0的特征值
                
                if len(eigenvalues) == 0:
                    return 0.0
                
                # 返回最大非零特征值
                return float(np.max(eigenvalues))
        
        return SimplifiedNLIMethod(method_class.__name__, model_name)
    
    def _create_cached_jaccard_method(self, method_class):
        """创建使用缓存Jaccard计算的方法实例"""
        method = method_class(verbose=False)
        # 替换原来的相似度计算方法
        
        def cached_compute_similarity(responses: List[str]):
            import numpy as np
            n = len(responses)
            W = np.zeros((n, n))
            
            for i in range(n):
                for j in range(n):
                    if i == j:
                        W[i, j] = 1.0
                    else:
                        W[i, j] = cached_jaccard_similarity(responses[i], responses[j])
            
            W = (W + np.transpose(W)) / 2
            return W
        
        method._compute_similarity_matrix = cached_compute_similarity
        return method

def split_twitter_response(response: str, prompt_type: str) -> Tuple[str, str]:
    """从Twitter响应中提取label和reasoning部分"""
    response = response.replace('\r\n', '\n').replace('\r', '\n')
    if prompt_type == "sentiment":
        return response.strip(), ""
    elif prompt_type in ("sentiment_reason", "sentiment_reason_first"):
        label_match = re.search(r"\[Label\]:\s*([^\n]+)", response)
        reasoning_match = re.search(r"\[Reasoning\]:\s*((?:.|\n)*?)(?=\n\[|$)", response)
        label = label_match.group(1).strip() if label_match else ""
        reasoning = reasoning_match.group(1).strip() if reasoning_match else ""
        return label, reasoning
    else:
        return response.strip(), ""

def split_commit_response(response: str, prompt_type: str) -> Tuple[str, str]:
    """从Commit响应中提取module和reasoning部分"""
    if prompt_type == 'single_word':
        return response.strip(), ""
    else:
        module, reasoning = '', ''
        module_match = re.search(r'Module:\s*(.*?)(?:\n|$)', response, re.DOTALL)
        reasoning_match = re.search(r'Reasoning:\s*(.*?)(?:\nModule:|$)', response, re.DOTALL)
        if module_match:
            module = module_match.group(1).strip()
        if reasoning_match:
            reasoning = reasoning_match.group(1).strip()
        return module, reasoning

def calculate_validation_accuracy(predicted_labels: List[str], validation_labels: List[str]) -> Dict[str, float]:
    """计算预测标签与validation标签的准确率"""
    if len(predicted_labels) != len(validation_labels):
        logger.warning(f"Length mismatch: predicted={len(predicted_labels)}, validation={len(validation_labels)}")
        return {'accuracy': 0.0, 'total_pairs': 0}
    
    correct = 0
    total = len(predicted_labels)
    
    for pred, val in zip(predicted_labels, validation_labels):
        # 简单的字符串匹配（可能需要根据具体情况调整）
        if pred.lower().strip() == val.lower().strip():
            correct += 1
    
    accuracy = correct / total if total > 0 else 0.0
    return {
        'accuracy': accuracy,
        'correct_predictions': correct,
        'total_pairs': total
    }

def analyze_all_uq_methods(csv_file: str, data_type: str, nli_model: str = "microsoft/deberta-large-mnli", 
                          max_identifiers: int = None):
    """分析所有数据的UQ方法"""
    print(f"\n=== Analyzing ALL {data_type} data with UQ methods ===")
    print(f"Using NLI model: {nli_model}")
    
    # 创建必要的目录
    create_directories()
    
    # 加载缓存
    load_cache()
    
    # 读取数据
    df = pd.read_csv(csv_file)
    print(f"Loaded {len(df)} records from {csv_file}")
    
    if data_type == 'twitter':
        identifier_col = 'tweet_index'
        sample_sizes = [10, 15, 20]  # 针对Twitter数据的sample sizes
    else:  # commit
        identifier_col = 'commit_sha'
        sample_sizes = [10, 15, 20, 25, 30]  # 针对Commit数据的sample sizes
    
    # 获取unique identifiers，并可能限制数量
    unique_identifiers = df[identifier_col].unique()
    if max_identifiers:
        unique_identifiers = unique_identifiers[:max_identifiers]
        logger.info(f"Limited to first {max_identifiers} identifiers")
    
    print(f"Found {len(unique_identifiers)} unique {identifier_col}s to process")
    
    # 初始化缓存的UQ方法
    logger.info("Initializing cached UQ methods...")
    cached_methods = CachedUQMethods(nli_model)
    logger.info(f"Initialized {len(cached_methods.methods)} UQ methods (NLI + Jaccard + NumSets versions)")
    
    results = []
    
    # 按identifier和prompt_type分组
    grouped = df.groupby([identifier_col, 'prompt_type'])
    logger.info(f"Processing {len(grouped)} groups ({identifier_col}, prompt_type combinations)")
    
    processed_count = 0
    for (identifier, prompt_type), group in tqdm(grouped, desc=f"Processing {data_type} groups"):
        # 如果设置了max_identifiers限制，跳过不在列表中的identifier
        if max_identifiers and identifier not in unique_identifiers:
            continue
            
        logger.info(f"Processing {identifier_col}={str(identifier)[:8]}..., prompt_type={prompt_type}")
        
        responses = group['response_text'].dropna().tolist()
        logger.info(f"Found {len(responses)} responses")
        
        if len(responses) < max(sample_sizes):
            logger.warning(f"Skipping: only {len(responses)} responses, need at least {max(sample_sizes)}")
            continue
        
        # 提取labels/modules和validation数据
        if data_type == 'twitter':
            content_items = []
            validation_items = []
            for idx, resp in enumerate(responses):
                label, _ = split_twitter_response(resp, prompt_type)
                if label:
                    content_items.append(label)
                    # 获取对应的validation标签
                    validation_label = group.iloc[idx]['validation'] if idx < len(group) else ""
                    validation_items.append(str(validation_label))
            content_type = 'label'
        else:  # commit
            validation_items = []  # Commit数据没有validation
            if prompt_type == 'single_word':
                content_items = responses
                content_type = 'response'
            else:
                content_items = []
                for resp in responses:
                    module, _ = split_commit_response(resp, prompt_type)
                    if module:
                        content_items.append(module)
                content_type = 'module'
        
        logger.info(f"Extracted {len(content_items)} {content_type}s")
        
        if len(content_items) < max(sample_sizes):
            logger.warning(f"Skipping: only {len(content_items)} {content_type}s available")
            continue
        
        # 对每个sample size进行测试
        for sample_size in sample_sizes:
            if len(content_items) < sample_size:
                logger.warning(f"Skipping sample_size={sample_size}: only {len(content_items)} items available")
                continue
                
            sample_items = content_items[:sample_size]
            sample_validation = validation_items[:sample_size] if validation_items else []
            logger.info(f"Testing with sample_size={sample_size}")
            
            # 计算validation准确率（仅对Twitter数据）
            validation_accuracy = {}
            if data_type == 'twitter' and validation_items:
                validation_accuracy = calculate_validation_accuracy(sample_items, sample_validation)
                logger.info(f"Validation accuracy: {validation_accuracy['accuracy']:.4f} ({validation_accuracy['correct_predictions']}/{validation_accuracy['total_pairs']})")
            
            # 对每个UQ方法进行测试
            for method_name, uq_method in cached_methods.methods.items():
                try:
                    logger.info(f"Computing uncertainty for {len(sample_items)} {content_type}s using {method_name}")
                    result = uq_method.compute_uncertainty(sample_items)
                    
                    # 处理不同方法的返回格式
                    if method_name == 'num_sets':
                        uncertainty_score = result.get('set_entropy', 0)
                        mean_similarity = 0  # NumSets不返回mean_similarity
                        additional_metrics = {
                            'num_sets': result.get('num_sets', 0),
                            'set_sizes': result.get('set_sizes', [])
                        }
                    else:
                        uncertainty_score = result.get('uncertainty_score', 0)
                        mean_similarity = result.get('mean_similarity', 0)
                        additional_metrics = {}
                    
                    logger.info(f"Result: uncertainty_score={uncertainty_score:.4f}, mean_similarity={mean_similarity:.4f}")
                    
                    result_dict = {
                        'data_type': data_type,
                        'identifier': str(identifier)[:12] if len(str(identifier)) > 12 else str(identifier),
                        'prompt_type': prompt_type,
                        'content_type': content_type,
                        'sample_size': sample_size,
                        'uq_method': method_name,
                        'uncertainty_score': uncertainty_score,
                        'mean_similarity': mean_similarity,
                        'method_details': result.get('method', method_name),
                        'sample_responses': sample_items[:3]
                    }
                    
                    # 添加validation准确率信息
                    if validation_accuracy:
                        result_dict.update({
                            'validation_accuracy': validation_accuracy['accuracy'],
                            'correct_predictions': validation_accuracy['correct_predictions'],
                            'total_validation_pairs': validation_accuracy['total_pairs']
                        })
                    
                    # 添加额外的指标
                    result_dict.update(additional_metrics)
                    results.append(result_dict)
                    
                except Exception as e:
                    logger.error(f"Error processing {method_name} with sample_size={sample_size}: {str(e)}")
        
        processed_count += 1
        if processed_count % 5 == 0:
            logger.info(f"Processed {processed_count} groups so far...")
            # 定期保存CSV格式的NLI缓存
            save_nli_csv_cache()
    
    # 保存CSV格式的NLI缓存
    save_nli_csv_cache()
    
    return results

def create_summary_tables(results_df: pd.DataFrame):
    """创建汇总表格，包括validation准确率"""
    print("\n=== Results Summary Tables ===")
    
    for data_type in results_df['data_type'].unique():
        type_df = results_df[results_df['data_type'] == data_type]
        print(f"\n--- {data_type.upper()} Data Summary ---")
        
        # 按prompt_type, sample_size, uq_method展示uncertainty_score
        pivot_uncertainty = type_df.pivot_table(
            index=['prompt_type', 'sample_size'], 
            columns='uq_method', 
            values='uncertainty_score',
            aggfunc='mean'
        ).round(4)
        print("\nUncertainty Scores:")
        print(pivot_uncertainty)
        
        # 按prompt_type, sample_size, uq_method展示mean_similarity
        pivot_similarity = type_df.pivot_table(
            index=['prompt_type', 'sample_size'], 
            columns='uq_method', 
            values='mean_similarity',
            aggfunc='mean'
        ).round(4)
        print("\nMean Similarity Scores:")
        print(pivot_similarity)
        
        # 对于Twitter数据，展示validation准确率
        if data_type == 'twitter' and 'validation_accuracy' in type_df.columns:
            pivot_validation = type_df.pivot_table(
                index=['prompt_type', 'sample_size'], 
                columns='uq_method', 
                values='validation_accuracy',
                aggfunc='mean'
            ).round(4)
            print("\nValidation Accuracy:")
            print(pivot_validation)
            
            # 保存validation准确率表
            validation_file = os.path.join(OUTPUT_DIR, f'{data_type}_validation_accuracy_pivot.csv')
            pivot_validation.to_csv(validation_file)
            print(f"Validation accuracy table saved to {validation_file}")
        
        # 保存详细的pivot表到CSV
        uncertainty_file = os.path.join(OUTPUT_DIR, f'{data_type}_uncertainty_pivot.csv')
        similarity_file = os.path.join(OUTPUT_DIR, f'{data_type}_similarity_pivot.csv')
        pivot_uncertainty.to_csv(uncertainty_file)
        pivot_similarity.to_csv(similarity_file)
        print(f"\nDetailed pivot tables saved to {OUTPUT_DIR}/{data_type}_*_pivot.csv")
        
        # 创建方法比较表（NLI vs Jaccard）
        print(f"\n--- Method Comparison ({data_type.upper()}) ---")
        for method_base in ['deg_mat', 'eccentricity', 'eig_val']:
            nli_method = f'{method_base}_nli'
            jaccard_method = f'{method_base}_jaccard'
            
            if nli_method in type_df['uq_method'].values and jaccard_method in type_df['uq_method'].values:
                comparison_df = type_df[type_df['uq_method'].isin([nli_method, jaccard_method])]
                
                # 基本指标比较
                comparison_pivot = comparison_df.pivot_table(
                    index=['prompt_type', 'sample_size'],
                    columns='uq_method',
                    values=['uncertainty_score', 'mean_similarity'],
                    aggfunc='mean'
                ).round(4)
                print(f"\n{method_base.upper()} Comparison (NLI vs Jaccard):")
                print(comparison_pivot)
                
                # 如果有validation数据，也进行比较
                if data_type == 'twitter' and 'validation_accuracy' in comparison_df.columns:
                    validation_comparison = comparison_df.pivot_table(
                        index=['prompt_type', 'sample_size'],
                        columns='uq_method',
                        values='validation_accuracy',
                        aggfunc='mean'
                    ).round(4)
                    print(f"\n{method_base.upper()} Validation Accuracy Comparison:")
                    print(validation_comparison)

def main():
    parser = argparse.ArgumentParser(description="Analyze UQ methods on ALL Twitter and Commit data with validation accuracy")
    parser.add_argument('--twitter_csv', type=str, default='data/all_twitter_responses.csv')
    parser.add_argument('--commit_csv', type=str, default='data/all_commit_responses.csv')
    parser.add_argument('--output_file', type=str, default=os.path.join(OUTPUT_DIR, 'uq_methods_complete_analysis.csv'))
    parser.add_argument('--nli_model', type=str, default='microsoft/deberta-large-mnli',
                       choices=['microsoft/deberta-large-mnli', 'cross-encoder/nli-deberta-v3-base', 'potsawee/deberta-v3-large-mnli'])
    parser.add_argument('--max_twitter_identifiers', type=int, default=None,
                       help='Limit number of Twitter identifiers to process (for testing)')
    parser.add_argument('--max_commit_identifiers', type=int, default=None,
                       help='Limit number of Commit identifiers to process (default: 50)')
    
    args = parser.parse_args()
    
    all_results = []
    
    # 分析Twitter数据
    try:
        print(f"Starting Twitter data analysis...")
        twitter_results = analyze_all_uq_methods(
            args.twitter_csv, 
            'twitter', 
            args.nli_model,
            max_identifiers=args.max_twitter_identifiers
        )
        all_results.extend(twitter_results)
        print(f"Completed Twitter analysis: {len(twitter_results)} results")
    except Exception as e:
        logger.error(f"Error analyzing Twitter data: {str(e)}")
        import traceback
        traceback.print_exc()
    
    # 分析Commit数据（由于数据量大，限制处理数量）
    try:
        print(f"Starting Commit data analysis (limited to {args.max_commit_identifiers} identifiers)...")
        commit_results = analyze_all_uq_methods(
            args.commit_csv, 
            'commit', 
            args.nli_model,
            max_identifiers=args.max_commit_identifiers
        )
        all_results.extend(commit_results)
        print(f"Completed Commit analysis: {len(commit_results)} results")
    except Exception as e:
        logger.error(f"Error analyzing Commit data: {str(e)}")
        import traceback
        traceback.print_exc()
    
    # 保存结果
    if all_results:
        results_df = pd.DataFrame(all_results)
        results_df.to_csv(args.output_file, index=False, encoding='utf-8-sig')
        print(f"\nAll results saved to {args.output_file}")
        print(f"Total results: {len(results_df)}")
        
        # 创建汇总表格
        create_summary_tables(results_df)
        
        # 显示基本统计信息
        print("\n=== Basic Statistics ===")
        print("Results by data type and method:")
        print(results_df.groupby(['data_type', 'uq_method']).size())
        
        print("\nSample sizes tested:")
        print(results_df.groupby(['data_type', 'sample_size']).size())
        
        print("\nMethod types:")
        results_df['method_type'] = results_df['uq_method'].apply(
            lambda x: 'NLI' if 'nli' in x else ('Jaccard' if 'jaccard' in x else 'NumSets')
        )
        results_df['method_base'] = results_df['uq_method'].apply(
            lambda x: x.replace('_nli', '').replace('_jaccard', '') if x != 'num_sets' else 'num_sets'
        )
        print(results_df.groupby(['data_type', 'method_type', 'method_base']).size())
        
        # 显示Twitter validation准确率统计
        if 'validation_accuracy' in results_df.columns:
            twitter_results = results_df[results_df['data_type'] == 'twitter']
            if not twitter_results.empty:
                print("\n=== Twitter Validation Accuracy Statistics ===")
                print("Average validation accuracy by method:")
                avg_accuracy = twitter_results.groupby('uq_method')['validation_accuracy'].mean().sort_values(ascending=False)
                print(avg_accuracy.round(4))
                
                print("\nValidation accuracy by prompt type:")
                prompt_accuracy = twitter_results.groupby(['prompt_type', 'uq_method'])['validation_accuracy'].mean()
                print(prompt_accuracy.round(4))
        
        # 显示缓存统计
        print(f"\nNLI CSV cache: {len(NLI_CSV_CACHE)} entries")
        
        # 最终保存CSV缓存
        print("\n=== Saving final CSV cache ===")
        save_nli_csv_cache()
        print("CSV cache saved successfully")
    else:
        print("No results generated")

if __name__ == "__main__":
    main()