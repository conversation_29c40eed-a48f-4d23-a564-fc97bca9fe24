#!/usr/bin/env python3
"""
测试统一NLI系统的完整功能
"""

import sys
import os
import pandas as pd

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_nli_cache_format():
    """测试NLI缓存格式升级"""
    print("=== 测试NLI缓存格式 ===")
    
    try:
        from analyze_all_uq_methods import (
            create_directories, load_nli_csv_cache, save_nli_csv_cache,
            cached_nli_scores, NLI_CSV_CACHE, NLI_CSV_CACHE_FILE
        )
        
        # 创建目录
        create_directories()
        
        # 加载现有缓存
        load_nli_csv_cache()
        old_cache_size = len(NLI_CSV_CACHE)
        print(f"加载现有缓存: {old_cache_size} 条目")
        
        # 测试新的NLI分数计算
        test_texts = [
            ("Hello", "Hi there"),
            ("Good morning", "Hello"),
            ("Yes", "No")
        ]
        
        print("\n计算测试文本对的NLI分数:")
        for text1, text2 in test_texts:
            print(f"  '{text1}' vs '{text2}'")
            try:
                result = cached_nli_scores(text1, text2, "microsoft/deberta-large-mnli")
                print(f"    Entailment: {result.entailment:.4f}")
                print(f"    Neutral: {result.neutral:.4f}")
                print(f"    Contradiction: {result.contradiction:.4f}")
                
                # 验证分数和约为1
                total = result.entailment + result.neutral + result.contradiction
                print(f"    Total: {total:.4f}")
                assert 0.95 < total < 1.05, f"分数和不合理: {total}"
                
            except Exception as e:
                print(f"    ❌ 计算失败: {e}")
                return False
        
        # 保存缓存
        print(f"\n保存缓存 (当前条目: {len(NLI_CSV_CACHE)})")
        save_nli_csv_cache()
        
        # 验证CSV文件格式
        if os.path.exists(NLI_CSV_CACHE_FILE):
            df = pd.read_csv(NLI_CSV_CACHE_FILE)
            print(f"CSV文件列: {list(df.columns)}")
            
            # 检查是否包含三个NLI分数
            required_cols = ['entailment', 'neutral', 'contradiction']
            missing_cols = [col for col in required_cols if col not in df.columns]
            
            if missing_cols:
                print(f"❌ 缺少必需列: {missing_cols}")
                return False
            else:
                print("✓ CSV格式正确，包含所有NLI分数")
                
            # 显示最新几条记录
            if len(df) > 0:
                print("\n最新的缓存记录:")
                recent_records = df.tail(3)
                for _, row in recent_records.iterrows():
                    print(f"  {row['text1'][:20]} -> {row['text2'][:20]}")
                    print(f"    E:{row['entailment']:.3f} N:{row['neutral']:.3f} C:{row['contradiction']:.3f}")
        
        print("✓ NLI缓存格式测试通过")
        return True
        
    except Exception as e:
        print(f"❌ NLI缓存格式测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_uq_methods_integration():
    """测试UQ方法与统一NLI系统的集成"""
    print("\n=== 测试UQ方法集成 ===")
    
    try:
        from analyze_all_uq_methods import CachedUQMethods
        
        # 创建UQ方法实例
        uq_methods = CachedUQMethods("microsoft/deberta-large-mnli")
        
        # 测试数据
        test_responses = [
            "The weather is sunny today.",
            "It's a bright and clear day.",
            "Today is rainy and cloudy.",
            "The weather is terrible."
        ]
        
        print(f"测试响应数量: {len(test_responses)}")
        
        # 测试每种NLI方法
        nli_methods = ['deg_mat_nli', 'eccentricity_nli', 'eig_val_nli']
        
        for method_name in nli_methods:
            print(f"\n测试方法: {method_name}")
            try:
                method = uq_methods.methods[method_name]
                
                # 计算相似度矩阵
                similarity_matrix = method._compute_similarity_matrix(test_responses)
                print(f"  相似度矩阵形状: {similarity_matrix.shape}")
                print(f"  矩阵值范围: [{similarity_matrix.min():.3f}, {similarity_matrix.max():.3f}]")
                
                # 验证矩阵是对称的
                is_symmetric = np.allclose(similarity_matrix, similarity_matrix.T, atol=1e-6)
                print(f"  矩阵对称性: {'✓' if is_symmetric else '❌'}")
                
                # 计算不确定性分数
                uncertainty = method.compute_uncertainty(test_responses)
                print(f"  不确定性分数: {uncertainty:.4f}")
                
                if not is_symmetric:
                    print(f"  ❌ {method_name} 矩阵不对称")
                    return False
                
                print(f"  ✓ {method_name} 正常工作")
                
            except Exception as e:
                print(f"  ❌ {method_name} 测试失败: {e}")
                import traceback
                traceback.print_exc()
                return False
        
        print("✓ UQ方法集成测试通过")
        return True
        
    except Exception as e:
        print(f"❌ UQ方法集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cache_persistence():
    """测试缓存持久化"""
    print("\n=== 测试缓存持久化 ===")
    
    try:
        from analyze_all_uq_methods import (
            NLI_CSV_CACHE, save_nli_csv_cache, load_nli_csv_cache,
            cached_nli_scores, NLI_CSV_CACHE_FILE
        )
        
        # 记录当前缓存大小
        initial_size = len(NLI_CSV_CACHE)
        print(f"初始缓存大小: {initial_size}")
        
        # 计算一个新的文本对
        import time
        unique_text1 = f"Test text at {time.time()}"
        unique_text2 = f"Another test text at {time.time()}"
        
        print(f"计算唯一文本对...")
        result = cached_nli_scores(unique_text1, unique_text2, "microsoft/deberta-large-mnli")
        print(f"  结果: E:{result.entailment:.3f} N:{result.neutral:.3f} C:{result.contradiction:.3f}")
        
        # 验证缓存增加了
        new_size = len(NLI_CSV_CACHE)
        print(f"新缓存大小: {new_size}")
        
        if new_size <= initial_size:
            print("❌ 缓存没有增加")
            return False
        
        # 保存缓存
        save_nli_csv_cache()
        print("✓ 缓存保存成功")
        
        # 清空内存缓存并重新加载
        NLI_CSV_CACHE.clear()
        load_nli_csv_cache()
        reloaded_size = len(NLI_CSV_CACHE)
        print(f"重新加载后缓存大小: {reloaded_size}")
        
        if reloaded_size < new_size:
            print("❌ 缓存持久化失败")
            return False
        
        # 验证特定条目存在
        test_key = f"{unique_text1}||{unique_text2}||microsoft/deberta-large-mnli"
        from analyze_all_uq_methods import get_text_hash
        text1_hash = get_text_hash(unique_text1)
        text2_hash = get_text_hash(unique_text2)
        cache_key = f"{text1_hash}||{text2_hash}||microsoft/deberta-large-mnli"
        
        if cache_key in NLI_CSV_CACHE:
            cached_entry = NLI_CSV_CACHE[cache_key]
            print(f"✓ 找到缓存条目: E:{cached_entry['entailment']:.3f}")
        else:
            print("❌ 缓存条目未找到")
            return False
        
        print("✓ 缓存持久化测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 缓存持久化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 统一NLI系统完整测试")
    print("=" * 60)
    
    # 导入numpy（在测试中需要）
    global np
    import numpy as np
    
    success = True
    
    # 测试缓存格式
    if not test_nli_cache_format():
        success = False
    
    # 测试UQ方法集成
    if not test_uq_methods_integration():
        success = False
    
    # 测试缓存持久化
    if not test_cache_persistence():
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 所有测试通过！统一NLI系统正常工作。")
        print("\n系统特性:")
        print("- ✅ 完整的三分数NLI计算 (entailment, neutral, contradiction)")
        print("- ✅ 统一的NLI计算器模块")
        print("- ✅ CSV格式缓存持久化")
        print("- ✅ 向后兼容性")
        print("- ✅ 简化的UQ方法实现")
        print("- ✅ 缓存一致性")
    else:
        print("❌ 部分测试失败，请检查错误信息")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)