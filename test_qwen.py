#!/usr/bin/env python3
"""
Qwen Uncertainty Framework Quick Test
Demonstrates the Qwen integration with streaming and NLI capabilities
"""
import asyncio
import os
from core.llm_client import QwenClient


async def test_qwen_client():
    """Test Qwen client functionality"""
    print("🚀 Testing Qwen Client Integration...")
    print("=" * 50)
    
    # Initialize Qwen client for Alibaba Cloud
    client = QwenClient(
        api_key=os.getenv("DASHSCOPE_API_KEY", "dummy_key"),
        model="qwen3-32b",
        base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
        nli_model="qwen3-latest"
    )
    
    try:
        # Test 1: Basic response generation
        print("\n📋 Test 1: Basic Response Generation")
        response = await client.generate_single_response(
            prompt="What is the relationship between AI and uncertainty?",
            system_prompt="You are an AI researcher answering questions about uncertainty in AI systems."
        )
        print(f"Response: {response[:200]}...")
        
        # Test 2: Stream response generation
        print("\n🌊 Test 2: Streaming Response")
        print("Streaming response: ", end="")
        async for chunk in client.generate_streaming_response(
            prompt="Explain uncertainty in machine learning in one sentence.",
            system_prompt="Be concise and accurate."
        ):
            print(chunk, end="")
        print()
        
        # Test 3: NLI Reasoning
        print("\n🔍 Test 3: NLI Reasoning")
        premise = "The cat is sleeping on the couch."
        hypothesis = "The cat is awake and moving around."
        nli_result = await client.generate_nli_reasoning(premise, hypothesis)
        print(f"Premise: {premise}")
        print(f"Hypothesis: {hypothesis}")
        print(f"NLI Result: {nli_result}")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
    finally:
        await client.close()


async def test_experiment_run():
    """Test experiment runner with Qwen configuration"""
    print("\n🧪 Testing Experiment Runner...")
    print("=" * 50)
    
    try:
        from run_experiment import SimpleExperimentRunner
        runner = SimpleExperimentRunner("config/qwen_experiment.yaml")
        print("✅ Experiment runner initialized successfully")
        print(f"Configuration loaded for: {runner.config['experiment']['name']}")
        print(f"Model: {runner.config['qwen']['model']}")
        print(f"NLI Model: {runner.config['qwen']['nli_model']}")
    except Exception as e:
        print(f"❌ Experiment runner initialization failed: {e}")


async def main():
    """Main test function"""
    print("🎯 Qwen Uncertainty Framework Test Suite")
    print("This script tests Qwen integration and streaming capabilities")
    print("Make sure Qwen services are running before testing")
    print()
    
    # Run basic client tests
    await test_qwen_client()
    
    # Run experiment runner test
    await test_experiment_run()
    
    print("\n✅ Test suite completed!")
    print("\nTo run a full experiment, use:")
    print("python run_experiment.py --config config/qwen_experiment.yaml")


if __name__ == "__main__":
    asyncio.run(main())