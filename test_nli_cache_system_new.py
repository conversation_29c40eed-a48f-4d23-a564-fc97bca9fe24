#!/usr/bin/env python3
"""
测试新的统一NLI缓存系统
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_nli_calculator():
    """测试NLI计算器基本功能"""
    print("=== 测试NLI计算器基本功能 ===")
    
    try:
        from core.nli_calculator import NLICalculator, CachedNLICalculator, NLIResult
        
        # 创建计算器
        calculator = CachedNLICalculator("microsoft/deberta-large-mnli", verbose=True)
        
        # 测试文本
        text1 = "The weather is sunny today."
        text2 = "It's a bright day."
        
        print(f"计算文本对的NLI分数:")
        print(f"Text1: {text1}")
        print(f"Text2: {text2}")
        
        # 计算NLI分数
        result = calculator.compute_nli_scores_cached(text1, text2)
        
        print(f"NLI结果:")
        print(f"  Entailment: {result.entailment:.4f}")
        print(f"  Neutral: {result.neutral:.4f}")
        print(f"  Contradiction: {result.contradiction:.4f}")
        
        # 验证分数加起来约等于1
        total = result.entailment + result.neutral + result.contradiction
        print(f"  总和: {total:.4f}")
        
        # 测试缓存
        print("\n再次计算相同文本对（应该从缓存获取）:")
        result2 = calculator.compute_nli_scores_cached(text1, text2)
        
        print(f"缓存结果:")
        print(f"  Entailment: {result2.entailment:.4f}")
        print(f"  Neutral: {result2.neutral:.4f}") 
        print(f"  Contradiction: {result2.contradiction:.4f}")
        
        # 验证结果一致
        assert abs(result.entailment - result2.entailment) < 1e-6
        print("✓ 缓存功能正常")
        
        return True
        
    except Exception as e:
        print(f"❌ NLI计算器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_analyze_methods_integration():
    """测试与analyze_all_uq_methods的集成"""
    print("\n=== 测试analyze_all_uq_methods集成 ===")
    
    try:
        from analyze_all_uq_methods import cached_nli_scores, NLI_CSV_CACHE, create_directories, load_nli_csv_cache, save_nli_csv_cache
        
        # 创建必要目录
        create_directories()
        
        # 加载现有缓存
        load_nli_csv_cache()
        print(f"加载的缓存条目数: {len(NLI_CSV_CACHE)}")
        
        # 测试文本
        text1 = "Good morning"
        text2 = "Hello there"
        model_name = "microsoft/deberta-large-mnli"
        
        print(f"计算NLI分数:")
        print(f"Text1: {text1}")
        print(f"Text2: {text2}")
        print(f"Model: {model_name}")
        
        # 计算NLI分数
        result = cached_nli_scores(text1, text2, model_name)
        
        print(f"NLI结果:")
        print(f"  Entailment: {result.entailment:.4f}")
        print(f"  Neutral: {result.neutral:.4f}")
        print(f"  Contradiction: {result.contradiction:.4f}")
        
        # 保存缓存
        save_nli_csv_cache()
        print("✓ 缓存保存成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 新的统一NLI缓存系统测试")
    print("=" * 50)
    
    success = True
    
    # 测试NLI计算器
    if not test_nli_calculator():
        success = False
    
    # 测试集成
    if not test_analyze_methods_integration():
        success = False
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 所有测试通过！新的NLI缓存系统正常工作。")
        print("\n缓存特性:")
        print("- ✅ 支持完整的三分数NLI计算 (entailment, neutral, contradiction)")
        print("- ✅ 统一的NLI计算器模块")
        print("- ✅ CSV格式缓存持久化")
        print("- ✅ 向后兼容旧缓存格式")
        print("- ✅ 内存缓存优化")
    else:
        print("❌ 部分测试失败，请检查错误信息")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)