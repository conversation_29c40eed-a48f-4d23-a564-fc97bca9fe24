import torch
from transformers import AutoTokenizer, AutoModelForSequenceClassification, DebertaV2Tokenizer
import numpy as np
from typing import List, Dict, Any
from uq_methods.base import BaseUQMethod
import networkx as nx

class NumSetsUQ(BaseUQMethod):
    """基于NLI的NumSets不确定性度量方法"""
    def __init__(self, model_name: str = "microsoft/deberta-large-mnli"):
        # 加载NLI模型
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # Handle specific model tokenizer loading
        if model_name == "potsawee/deberta-v3-large-mnli":
            try:
                # Try using DebertaV2Tokenizer for this specific model
                self.tokenizer = DebertaV2Tokenizer.from_pretrained(model_name)
            except:
                # Fallback to AutoTokenizer with use_fast=False
                self.tokenizer = AutoTokenizer.from_pretrained(model_name, use_fast=False)
        else:
            self.tokenizer = AutoTokenizer.from_pretrained(model_name)
            
        self.model = AutoModelForSequenceClassification.from_pretrained(model_name).to(self.device)
        self.model.eval()

    def pairwise_nli(self, text1: str, text2: str) -> str:
        """对两个文本做NLI推理，返回'entailment'/'contradiction'/'neutral'"""
        inputs = self.tokenizer(text1, text2, return_tensors="pt", truncation=True, max_length=256).to(self.device)
        with torch.no_grad():
            logits = self.model(**inputs).logits
            probs = torch.softmax(logits, dim=-1).cpu().numpy()[0]
        label_id = np.argmax(probs)
        label_map = {0: "contradiction", 1: "neutral", 2: "entailment"}
        return label_map[label_id]

    def compute_uncertainty(self, responses: List[str]) -> Dict[str, Any]:
        """主流程：两两NLI+连通分量+集合熵"""
        n = len(responses)
        if n < 2:
            return {"error": "Need at least 2 responses"}
        # 构建无向图
        G = nx.Graph()
        G.add_nodes_from(range(n))
        nli_results = {}
        for i in range(n):
            for j in range(n):
                if i == j:
                    continue
                label_ij = self.pairwise_nli(responses[i], responses[j])
                nli_results[(i, j)] = label_ij
        # 连边：只有(i->j)和(j->i)都为'entailment'才连
        for i in range(n):
            for j in range(i+1, n):
                if nli_results[(i, j)] == "entailment" and nli_results[(j, i)] == "entailment":
                    G.add_edge(i, j)
        # 连通分量分组
        components = list(nx.connected_components(G))
        set_sizes = [len(c) for c in components]
        total = sum(set_sizes)
        probs = [s/total for s in set_sizes]
        entropy = -sum(p * np.log(p) for p in probs if p > 0)
        # 结果组织
        result = {
            "num_sets": len(components),
            "set_sizes": set_sizes,
            "set_entropy": entropy,
            "sets": [[responses[idx] for idx in comp] for comp in components],
            "nli_results": {f"{i}_{j}": nli_results[(i, j)] for (i, j) in nli_results}
        }
        return result

    def get_required_samples(self) -> int:
        return 10 