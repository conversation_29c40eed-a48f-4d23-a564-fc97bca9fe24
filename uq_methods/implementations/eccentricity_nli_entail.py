import numpy as np
import logging
import torch
from typing import List, Dict, Any
from scipy.linalg import eigh
from transformers import AutoTokenizer, AutoModelForSequenceClassification
from uq_methods.base import BaseUQMethod

log = logging.getLogger(__name__)


class EccentricityNLIEntailUQ(BaseUQMethod):
    """
    Eccentricity method using NLI Score with Entail affinity for similarity computation.
    Estimates the sequence-level uncertainty of a language model following the method of
    "Eccentricity" as provided in the paper https://arxiv.org/abs/2305.19187.
    
    Method calculates a frobenious (euclidian) norm between all eigenvectors that are informative embeddings
    of graph Laplacian (lower norm -> closer embeddings -> higher eigenvectors -> greater uncertainty).
    """

    def __init__(self, model_name: str = "microsoft/deberta-large-mnli", verbose: bool = False, thres: float = 0.9):
        """
        Initialize Eccentricity with NLI score similarity using entail affinity.
        
        Parameters:
            model_name (str): NLI model to use for similarity computation
            verbose (bool): Whether to print debug information
            thres (float): Threshold for eigenvalue filtering
        """
        self.model_name = model_name
        self.verbose = verbose
        self.thres = thres
        
        # Initialize NLI model
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)
        self.model = AutoModelForSequenceClassification.from_pretrained(model_name).to(self.device)
        self.model.eval()

    def _compute_nli_scores(self, text1: str, text2: str) -> tuple:
        """Compute all three NLI scores between two texts."""
        try:
            inputs = self.tokenizer(
                text1, text2, 
                return_tensors="pt", 
                truncation=True, 
                max_length=256
            ).to(self.device)
            
            with torch.no_grad():
                logits = self.model(**inputs).logits
                probs = torch.softmax(logits, dim=-1).cpu().numpy()[0]
            
            # Label mapping: 0=contradiction, 1=neutral, 2=entailment
            contradiction_score = float(probs[0])
            neutral_score = float(probs[1])
            entailment_score = float(probs[2])
            
            return entailment_score, neutral_score, contradiction_score
            
        except Exception as e:
            log.warning(f"Error computing NLI score: {str(e)}")
            return 0.33, 0.34, 0.33  # Default to uniform distribution
    
    def _compute_nli_entail_score(self, text1: str, text2: str) -> float:
        """Compute NLI entailment score between two texts (backward compatibility)."""
        entailment, _, _ = self._compute_nli_scores(text1, text2)
        return entailment

    def _compute_similarity_matrix(self, responses: List[str]) -> np.ndarray:
        """Compute similarity matrix using NLI entailment scores."""
        n = len(responses)
        W = np.zeros((n, n))
        
        for i in range(n):
            for j in range(n):
                if i == j:
                    W[i, j] = 1.0
                else:
                    # Compute bidirectional NLI scores
                    score_ij = self._compute_nli_entail_score(responses[i], responses[j])
                    score_ji = self._compute_nli_entail_score(responses[j], responses[i])
                    
                    # Use the average of bidirectional scores
                    W[i, j] = (score_ij + score_ji) / 2
        
        W = (W + np.transpose(W)) / 2
        return W

    def _compute_eccentricity(self, responses: List[str]) -> float:
        """Compute the eccentricity uncertainty score."""
        if len(responses) < 2:
            return 0.0
            
        W = self._compute_similarity_matrix(responses)
        D = np.diag(W.sum(axis=1))
        D_sqrt = np.sqrt(D)
        D_sqrt_inv = np.linalg.inv(D_sqrt)
        L = np.eye(D.shape[0]) - D_sqrt_inv @ W @ D_sqrt_inv
        
        eigenvalues, eigenvectors = eigh(L)
        
        if self.thres is not None:
            keep_mask = eigenvalues < self.thres
            eigenvalues = eigenvalues[keep_mask]
            eigenvectors = eigenvectors[:, keep_mask]
        
        smallest_eigenvectors = eigenvectors.T
        C_Ecc_s_j = (-1) * np.asarray(
            [np.linalg.norm(x - x.mean(0), 2) for x in smallest_eigenvectors]
        )
        U_Ecc = np.linalg.norm(C_Ecc_s_j, 2)
        
        return U_Ecc

    def compute_uncertainty(self, responses: List[str]) -> Dict[str, Any]:
        """Compute uncertainty using Eccentricity with NLI entailment similarity."""
        if len(responses) < 2:
            return {
                "uncertainty_score": 0.0,
                "error": "Need at least 2 responses",
                "method": "Eccentricity_NLI_Entail"
            }
        
        try:
            uncertainty_score = self._compute_eccentricity(responses)
            similarity_matrix = self._compute_similarity_matrix(responses)
            mean_similarity = np.mean(similarity_matrix[np.triu_indices_from(similarity_matrix, k=1)])
            
            if self.verbose:
                log.debug(f"Generated responses: {responses}")
                log.debug(f"Uncertainty score: {uncertainty_score}")
                log.debug(f"Mean similarity: {mean_similarity}")
            
            return {
                "uncertainty_score": uncertainty_score,
                "mean_similarity": mean_similarity,
                "num_responses": len(responses),
                "method": "Eccentricity_NLI_Entail",
                "similarity_matrix": similarity_matrix.tolist(),
                "metadata": {
                    "method": "Eccentricity_NLI_Entail",
                    "similarity_score": "NLI_score",
                    "affinity": "entail",
                    "model_name": self.model_name,
                    "thres": self.thres,
                    "verbose": self.verbose
                }
            }
            
        except Exception as e:
            log.error(f"Error computing Eccentricity NLI Entail uncertainty: {str(e)}")
            return {
                "uncertainty_score": 1.0,
                "error": str(e),
                "method": "Eccentricity_NLI_Entail"
            }

    def get_required_samples(self) -> int:
        return 5

    def get_method_name(self) -> str:
        return "Eccentricity_NLI_Entail" 