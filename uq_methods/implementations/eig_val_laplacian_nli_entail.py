import numpy as np
import logging
import torch
from typing import List, Dict, Any
from scipy.linalg import eigh
from transformers import AutoTokenizer, AutoModelForSequenceClassification
from uq_methods.base import BaseUQMethod

log = logging.getLogger(__name__)


class EigValLaplacianNLIEntailUQ(BaseUQMethod):
    """
    EigValLaplacian method using NLI Score with Entail affinity for similarity computation.
    Estimates the sequence-level uncertainty of a language model following the method of
    "Sum of Eigenvalues of the Graph Laplacian" as provided in the paper https://arxiv.org/abs/2305.19187.
    
    A continuous analogue to the number of semantic sets (higher values means greater uncertainty).
    """

    def __init__(self, model_name: str = "microsoft/deberta-large-mnli", verbose: bool = False):
        """
        Initialize EigValLaplacian with NLI score similarity using entail affinity.
        
        Parameters:
            model_name (str): NLI model to use for similarity computation
            verbose (bool): Whether to print debug information
        """
        self.model_name = model_name
        self.verbose = verbose
        
        # Initialize NLI model
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)
        self.model = AutoModelForSequenceClassification.from_pretrained(model_name).to(self.device)
        self.model.eval()

    def _compute_nli_entail_score(self, text1: str, text2: str) -> float:
        """
        Compute NLI entailment score between two texts.
        
        Args:
            text1: First text (premise)
            text2: Second text (hypothesis)
            
        Returns:
            Entailment probability score between 0 and 1
        """
        try:
            inputs = self.tokenizer(
                text1, text2, 
                return_tensors="pt", 
                truncation=True, 
                max_length=256
            ).to(self.device)
            
            with torch.no_grad():
                logits = self.model(**inputs).logits
                probs = torch.softmax(logits, dim=-1).cpu().numpy()[0]
            
            # Label mapping: 0=contradiction, 1=neutral, 2=entailment
            entailment_score = probs[2]  # Probability of entailment
            
            return entailment_score
            
        except Exception as e:
            log.warning(f"Error computing NLI score: {str(e)}")
            return 0.5  # Default to neutral if error occurs

    def _compute_similarity_matrix(self, responses: List[str]) -> np.ndarray:
        """
        Compute similarity matrix using NLI entailment scores.
        
        Args:
            responses: List of response texts
            
        Returns:
            Similarity matrix W
        """
        n = len(responses)
        W = np.zeros((n, n))
        
        for i in range(n):
            for j in range(n):
                if i == j:
                    W[i, j] = 1.0  # Self-similarity is 1
                else:
                    # Compute bidirectional NLI scores
                    score_ij = self._compute_nli_entail_score(responses[i], responses[j])
                    score_ji = self._compute_nli_entail_score(responses[j], responses[i])
                    
                    # Use the average of bidirectional scores
                    W[i, j] = (score_ij + score_ji) / 2
        
        # Make matrix symmetric
        W = (W + np.transpose(W)) / 2
        return W

    def _compute_eigval_laplacian(self, responses: List[str]) -> float:
        """
        Compute the sum of eigenvalues of the graph Laplacian.
        
        Args:
            responses: List of response texts
            
        Returns:
            Sum of eigenvalues (uncertainty score)
        """
        if len(responses) < 2:
            return 0.0
            
        # Compute similarity matrix
        W = self._compute_similarity_matrix(responses)
        
        # Compute degree matrix
        D = np.diag(W.sum(axis=1))
        
        # Avoid division by zero
        D_sqrt = np.sqrt(D)
        D_sqrt_inv = np.linalg.inv(D_sqrt)
        
        # Compute normalized Laplacian
        L = np.eye(D.shape[0]) - D_sqrt_inv @ W @ D_sqrt_inv
        
        # Compute eigenvalues and sum the positive ones
        eigenvalues = eigh(L, eigvals_only=True)
        uncertainty_score = sum([max(0, 1 - lambda_k) for lambda_k in eigenvalues])
        
        return uncertainty_score

    def compute_uncertainty(self, responses: List[str]) -> Dict[str, Any]:
        """
        Compute uncertainty using EigValLaplacian with NLI entailment similarity.
        
        Args:
            responses: List of response texts for the same prompt
            
        Returns:
            Dictionary containing uncertainty metrics
        """
        if len(responses) < 2:
            return {
                "uncertainty_score": 0.0,
                "error": "Need at least 2 responses",
                "method": "EigValLaplacian_NLI_Entail"
            }
        
        try:
            # Compute uncertainty score
            uncertainty_score = self._compute_eigval_laplacian(responses)
            
            # Compute additional metrics
            similarity_matrix = self._compute_similarity_matrix(responses)
            mean_similarity = np.mean(similarity_matrix[np.triu_indices_from(similarity_matrix, k=1)])
            
            if self.verbose:
                log.debug(f"Generated responses: {responses}")
                log.debug(f"Uncertainty score: {uncertainty_score}")
                log.debug(f"Mean similarity: {mean_similarity}")
            
            return {
                "uncertainty_score": uncertainty_score,
                "mean_similarity": mean_similarity,
                "num_responses": len(responses),
                "method": "EigValLaplacian_NLI_Entail",
                "similarity_matrix": similarity_matrix.tolist(),
                "metadata": {
                    "method": "EigValLaplacian_NLI_Entail",
                    "similarity_score": "NLI_score",
                    "affinity": "entail",
                    "model_name": self.model_name,
                    "verbose": self.verbose
                }
            }
            
        except Exception as e:
            log.error(f"Error computing EigValLaplacian NLI Entail uncertainty: {str(e)}")
            return {
                "uncertainty_score": 1.0,
                "error": str(e),
                "method": "EigValLaplacian_NLI_Entail"
            }

    def get_required_samples(self) -> int:
        """Return the number of samples required for this method."""
        return 5

    def get_method_name(self) -> str:
        """Get the method name."""
        return "EigValLaplacian_NLI_Entail" 