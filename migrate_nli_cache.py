#!/usr/bin/env python3
"""
NLI缓存格式迁移脚本

将旧的NLI缓存格式（只有similarity_score）迁移到新格式（包含entailment, neutral, contradiction三个分数）
"""

import os
import sys
import pandas as pd
import hashlib
import time

def get_text_hash(text: str) -> str:
    """生成文本的哈希值"""
    return hashlib.md5(text.encode('utf-8')).hexdigest()

def migrate_cache_file(cache_file: str, backup: bool = True):
    """
    迁移单个缓存文件
    
    Args:
        cache_file: 缓存文件路径
        backup: 是否创建备份
    """
    if not os.path.exists(cache_file):
        print(f"❌ 缓存文件不存在: {cache_file}")
        return False
    
    try:
        # 读取原始缓存
        df = pd.read_csv(cache_file)
        print(f"📖 读取缓存文件: {cache_file}")
        print(f"   原始记录数: {len(df)}")
        print(f"   原始列: {list(df.columns)}")
        
        # 检查是否需要迁移
        if 'entailment' in df.columns and 'neutral' in df.columns and 'contradiction' in df.columns:
            print("✅ 缓存已经是新格式，无需迁移")
            return True
        
        # 创建备份
        if backup:
            backup_file = cache_file + '.backup'
            df.to_csv(backup_file, index=False, encoding='utf-8-sig')
            print(f"💾 创建备份: {backup_file}")
        
        # 迁移数据
        migrated_count = 0
        for idx, row in df.iterrows():
            if 'similarity_score' in row and pd.notna(row['similarity_score']):
                # 将similarity_score作为entailment分数
                df.at[idx, 'entailment'] = row['similarity_score']
                df.at[idx, 'neutral'] = 0.33
                df.at[idx, 'contradiction'] = 0.33
                migrated_count += 1
        
        # 删除旧的similarity_score列
        if 'similarity_score' in df.columns:
            df = df.drop('similarity_score', axis=1)
        
        # 保存迁移后的文件
        df.to_csv(cache_file, index=False, encoding='utf-8-sig')
        print(f"✅ 迁移完成: {migrated_count} 条记录")
        print(f"   新列: {list(df.columns)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 迁移失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主迁移函数"""
    print("🔄 NLI缓存格式迁移工具")
    print("=" * 50)
    
    # 默认缓存文件路径
    cache_files = [
        "cache/nli_results_cache.csv",
        "cache/nli_results_cache_*.csv"
    ]
    
    import glob
    
    all_files = []
    for pattern in cache_files:
        if '*' in pattern:
            all_files.extend(glob.glob(pattern))
        else:
            if os.path.exists(pattern):
                all_files.append(pattern)
    
    if not all_files:
        print("❌ 未找到需要迁移的缓存文件")
        print("请确保以下文件存在:")
        for pattern in cache_files:
            print(f"  - {pattern}")
        return 1
    
    print(f"找到 {len(all_files)} 个缓存文件:")
    for file in all_files:
        print(f"  - {file}")
    
    # 确认迁移
    response = input("\n是否继续迁移？(y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        print("❌ 用户取消迁移")
        return 0
    
    # 执行迁移
    success_count = 0
    for cache_file in all_files:
        print(f"\n--- 迁移文件: {cache_file} ---")
        if migrate_cache_file(cache_file):
            success_count += 1
    
    print("\n" + "=" * 50)
    print(f"迁移完成: {success_count}/{len(all_files)} 个文件成功")
    
    if success_count == len(all_files):
        print("🎉 所有缓存文件迁移成功！")
        print("\n新格式特性:")
        print("- ✅ 包含完整的三个NLI分数 (entailment, neutral, contradiction)")
        print("- ✅ 向后兼容旧格式")
        print("- ✅ 自动缓存管理")
        print("- ✅ 统一的计算器模块")
    else:
        print("⚠️ 部分文件迁移失败，请检查错误信息")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 