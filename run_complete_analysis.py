#!/usr/bin/env python3
"""
运行完整的UQ方法分析脚本
该脚本分析所有Twitter和Commit数据，包括validation准确率计算

修改点:
1. NLI处理结果阶段性保存到cache文件夹（使用结构化CSV格式）
2. 所有结果保存到data/Uq_Evaluation_20250731目录
3. 新增结构化NLI缓存系统，便于后续分析复用
4. 可使用nli_cache_manager.py管理和查看缓存
"""

import subprocess
import sys
import argparse

def run_analysis(max_twitter_identifiers=None, max_commit_identifiers=None, nli_model="microsoft/deberta-large-mnli"):
    """
    运行完整的UQ方法分析
    
    Args:
        max_twitter_identifiers: 限制Twitter数据的identifier数量（None表示处理所有）
        max_commit_identifiers: 限制Commit数据的identifier数量（默认50，因为数据量很大）
        nli_model: 使用的NLI模型名称
    """
    
    cmd = ["python", "analyze_all_uq_methods.py"]
    
    # 添加参数
    cmd.extend([
        "--twitter_csv", "data/all_twitter_responses.csv",
        "--commit_csv", "data/all_commit_responses.csv",
        "--output_file", "data/Uq_Evaluation_20250731/uq_methods_complete_analysis.csv",
        "--nli_model", nli_model
    ])
    
    if max_twitter_identifiers:
        cmd.extend(["--max_twitter_identifiers", str(max_twitter_identifiers)])
    
    if max_commit_identifiers:
        cmd.extend(["--max_commit_identifiers", str(max_commit_identifiers)])
    
    print(f"Running command: {' '.join(cmd)}")
    print(f"Twitter identifiers limit: {'ALL' if max_twitter_identifiers is None else max_twitter_identifiers}")
    print(f"Commit identifiers limit: {'ALL' if max_commit_identifiers is None else max_commit_identifiers}")
    print(f"NLI model: {nli_model}")
    print("="*50)
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=False)
        print("\n" + "="*50)
        print("Analysis completed successfully!")
        print("Results saved to: data/Uq_Evaluation_20250731/uq_methods_complete_analysis.csv")
        print("Summary tables saved to: data/Uq_Evaluation_20250731/*_pivot.csv")
        print("NLI cache saved to: cache/nli_cache*.pkl")
        print("Structured NLI cache (CSV) saved to: cache/nli_results_cache.csv")
        print("Timestamped NLI caches saved to: cache/nli_results_cache_*.csv")
        print("")
        print("Use 'python nli_cache_manager.py stats' to view cache statistics")
        print("Use 'python nli_cache_manager.py --help' for cache management options")
        
    except subprocess.CalledProcessError as e:
        print(f"Error running analysis: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\nAnalysis interrupted by user")
        sys.exit(1)

def main():
    parser = argparse.ArgumentParser(description="Run complete UQ methods analysis")
    parser.add_argument('--max_twitter_identifiers', type=int, default=None,
                       help='Limit number of Twitter identifiers (default: process all)')
    parser.add_argument('--max_commit_identifiers', type=int, default=None,
                       help='Limit number of Commit identifiers (default: 50)')
    parser.add_argument('--nli_model', type=str, default='microsoft/deberta-large-mnli',
                       choices=['microsoft/deberta-large-mnli', 'cross-encoder/nli-deberta-v3-base', 'potsawee/deberta-v3-large-mnli'],
                       help='NLI model to use for similarity computation')
    parser.add_argument('--quick_test', action='store_true',
                       help='Run a quick test with limited data')
    
    args = parser.parse_args()
    
    if args.quick_test:
        print("Running quick test with limited data...")
        run_analysis(
            max_twitter_identifiers=5, 
            max_commit_identifiers=5, 
            nli_model=args.nli_model
        )
    else:
        run_analysis(
            max_twitter_identifiers=args.max_twitter_identifiers,
            max_commit_identifiers=args.max_commit_identifiers,
            nli_model=args.nli_model
        )

if __name__ == "__main__":
    main()