import pandas as pd
from pymongo import MongoClient
import argparse

def export_all_twitter_data_to_csv(mongo_host='localhost', mongo_port=27017, 
                                  mongo_db='twitter_sentiment', mongo_collection='responses',
                                  output_file='data/all_twitter_responses.csv'):
    """
    从MongoDB导出所有Twitter响应数据到CSV文件
    """
    print(f"Connecting to MongoDB: {mongo_host}:{mongo_port}")
    
    # 连接MongoDB
    client = MongoClient(host=mongo_host, port=mongo_port)
    db = client[mongo_db]
    collection = db[mongo_collection]
    
    # 获取总记录数
    total_count = collection.count_documents({})
    print(f"Total documents in collection: {total_count}")
    
    if total_count == 0:
        print("No documents found in the collection")
        client.close()
        return
    
    # 获取所有数据，按tweet_index和prompt_type排序
    print("Fetching all documents...")
    cursor = collection.find({}).sort([("tweet_index", 1), ("prompt_type", 1)])
    
    # 转换为列表（分批处理以避免内存问题）
    documents = []
    batch_size = 10000
    batch_count = 0
    
    for doc in cursor:
        documents.append(doc)
        batch_count += 1
        
        if batch_count % batch_size == 0:
            print(f"Processed {batch_count}/{total_count} documents...")
    
    print(f"Fetched {len(documents)} documents")
    
    # 转换为DataFrame
    df = pd.DataFrame(documents)
    
    # 选择需要的列（如果存在的话）
    available_columns = df.columns.tolist()
    desired_columns = ['tweet_index', 'prompt_type', 'text', 'validation', 
                      'response_text', 'model_name', 'query_index', 'timestamp']
    
    # 只保留存在的列
    columns_to_keep = [col for col in desired_columns if col in available_columns]
    print(f"Available columns: {available_columns}")
    print(f"Keeping columns: {columns_to_keep}")
    
    df = df[columns_to_keep]
    
    # 保存到CSV
    print(f"Saving to {output_file}...")
    df.to_csv(output_file, index=False, encoding='utf-8-sig')
    
    print(f"Successfully exported {len(df)} records to {output_file}")
    
    # 显示统计信息
    if 'tweet_index' in df.columns:
        print(f"Unique tweets: {df['tweet_index'].nunique()}")
    if 'prompt_type' in df.columns:
        print(f"Prompt types: {df['prompt_type'].unique().tolist()}")
        
        # 显示每个tweet和prompt_type的响应数量统计
        if 'tweet_index' in df.columns:
            summary = df.groupby(['tweet_index', 'prompt_type']).size()
            print(f"\nSample of responses per tweet and prompt type:")
            print(summary.head(10))
            
            # 整体统计
            print(f"\nOverall statistics:")
            print(f"- Total tweets: {df['tweet_index'].nunique()}")
            print(f"- Average responses per (tweet, prompt_type): {summary.mean():.1f}")
            print(f"- Response count distribution:")
            print(summary.value_counts().sort_index())
    
    client.close()
    return df

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Export all Twitter data from MongoDB to CSV")
    parser.add_argument('--mongo_host', type=str, default='localhost')
    parser.add_argument('--mongo_port', type=int, default=27017)
    parser.add_argument('--mongo_db', type=str, default='twitter_sentiment')
    parser.add_argument('--mongo_collection', type=str, default='responses')
    parser.add_argument('--output_file', type=str, default='data/all_twitter_responses.csv')
    
    args = parser.parse_args()
    
    export_all_twitter_data_to_csv(
        mongo_host=args.mongo_host,
        mongo_port=args.mongo_port,
        mongo_db=args.mongo_db,
        mongo_collection=args.mongo_collection,
        output_file=args.output_file
    )