#!/usr/bin/env python3
"""
Test script to find the correct Qwen API request format.
This will test different ways to set enable_thinking=False for Qwen models.
"""

import os
import sys
import json
from typing import List, Dict, Any

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from openai import OpenAI
except ImportError:
    OpenAI = None
    print("Warning: openai package not installed. Install with: pip install openai")

# Test configurations
DASHSCOPE_API_KEY = os.getenv("DASHSCOPE_API_KEY", "sk-8a360a0f66fa416ba7ac5b428d39b0a5")
BASE_URL = "https://dashscope.aliyuncs.com/compatible-mode/v1"
MODEL_NAME = "qwen3-32b"

def test_format_1():
    """Test using top-level enable_thinking parameter"""
    print("=== Test Format 1: Top-level enable_thinking ===")
    try:
        client = OpenAI(api_key=DASHSCOPE_API_KEY, base_url=BASE_URL)
        
        response = client.chat.completions.create(
            model=MODEL_NAME,
            messages=[{"role": "user", "content": "What is the capital of France?"}],
            enable_thinking=False,
            max_tokens=50
        )
        print("✅ Format 1 SUCCESS")
        print(f"Response: {response.choices[0].message.content}")
        return True
    except Exception as e:
        print(f"❌ Format 1 FAILED: {e}")
        return False

def test_format_2():
    """Test using extra_body with chat_template_kwargs"""
    print("\n=== Test Format 2: extra_body with chat_template_kwargs ===")
    try:
        client = OpenAI(api_key=DASHSCOPE_API_KEY, base_url=BASE_URL)
        
        response = client.chat.completions.create(
            model=MODEL_NAME,
            messages=[{"role": "user", "content": "What is the capital of France?"}],
            extra_body={
                "chat_template_kwargs": {
                    "enable_thinking": False
                }
            },
            max_tokens=50
        )
        print("✅ Format 2 SUCCESS")
        print(f"Response: {response.choices[0].message.content}")
        return True
    except Exception as e:
        print(f"❌ Format 2 FAILED: {e}")
        return False

def test_format_3():
    """Test using parameters in messages"""
    print("\n=== Test Format 3: Using system message with thinking instructions ===")
    try:
        client = OpenAI(api_key=DASHSCOPE_API_KEY, base_url=BASE_URL)
        
        response = client.chat.completions.create(
            model=MODEL_NAME,
            messages=[
                {"role": "system", "content": "/set nothink"},
                {"role": "user", "content": "What is the capital of France?"}
            ],
            max_tokens=50
        )
        print("✅ Format 3 SUCCESS")
        print(f"Response: {response.choices[0].message.content}")
        return True
    except Exception as e:
        print(f"❌ Format 3 FAILED: {e}")
        return False

def test_format_4():
    """Test using top-level parameters dict"""
    print("\n=== Test Format 4: Using parameters dict ===")
    try:
        client = OpenAI(api_key=DASHSCOPE_API_KEY, base_url=BASE_URL)
        
        response = client.chat.completions.create(
            model=MODEL_NAME,
            messages=[{"role": "user", "content": "What is the capital of France?"}],
            parameters={
                "enable_thinking": False
            },
            max_tokens=50
        )
        print("✅ Format 4 SUCCESS")
        print(f"Response: {response.choices[0].message.content}")
        return True
    except Exception as e:
        print(f"❌ Format 4 FAILED: {e}")
        return False

def test_format_5():
    """Test using stream=False explicitly"""
    print("\n=== Test Format 5: With stream=False ===")
    try:
        client = OpenAI(api_key=DASHSCOPE_API_KEY, base_url=BASE_URL)
        
        response = client.chat.completions.create(
            model=MODEL_NAME,
            messages=[{"role": "user", "content": "What is the capital of France?"}],
            stream=False,
            max_tokens=50
        )
        print("✅ Format 5 SUCCESS")
        print(f"Response: {response.choices[0].message.content}")
        return True
    except Exception as e:
        print(f"❌ Format 5 FAILED: {e}")
        return False

def test_format_6():
    """Test using body parameter"""
    print("\n=== Test Format 6: Using body parameter ===")
    try:
        client = OpenAI(api_key=DASHSCOPE_API_KEY, base_url=BASE_URL)
        
        response = client.chat.completions.create(
            model=MODEL_NAME,
            messages=[{"role": "user", "content": "What is the capital of France?"}],
            body={
                "enable_thinking": False,
                "model": MODEL_NAME,
                "messages": [{"role": "user", "content": "What is the capital of France?"}],
                "max_tokens": 50
            }
        )
        print("✅ Format 6 SUCCESS")
        print(f"Response: {response.choices[0].message.content}")
        return True
    except Exception as e:
        print(f"❌ Format 6 FAILED: {e}")
        return False

def test_all_formats():
    """Test all formats and report results"""
    print("Testing Qwen API request formats...")
    print(f"API Key: {DASHSCOPE_API_KEY[:10]}...")
    print(f"Base URL: {BASE_URL}")
    print(f"Model: {MODEL_NAME}")
    print()
    
    results = []
    results.append(("Format 1: Top-level enable_thinking", test_format_1()))
    results.append(("Format 2: extra_body", test_format_2()))
    results.append(("Format 3: System message", test_format_3()))
    results.append(("Format 4: Parameters dict", test_format_4()))
    results.append(("Format 5: Stream=False", test_format_5()))
    results.append(("Format 6: Body parameter", test_format_6()))
    
    print("\n" + "="*50)
    print("SUMMARY:")
    successful_formats = [name for name, success in results if success]
    failed_formats = [name for name, success in results if not success]
    
    print(f"Successful formats: {len(successful_formats)}")
    for name in successful_formats:
        print(f"  ✅ {name}")
    
    print(f"Failed formats: {len(failed_formats)}")
    for name in failed_formats:
        print(f"  ❌ {name}")
    
    return results

if __name__ == "__main__":
    if OpenAI is None:
        print("Error: openai package not installed. Please install with: pip install openai")
        sys.exit(1)
    
    test_all_formats()