"""
Unified OpenAI client wrapper for consistent API calls across different providers.

Supports:
- <PERSON>wen (Alibaba Cloud DashScope)
- OpenAI
- Azure OpenAI
- Local models via OpenAI-compatible endpoints
"""

import os
import time
from typing import Dict, List, Optional, Any
import json
from abc import ABC, abstractmethod

try:
    from openai import OpenAI
except ImportError:
    OpenAI = None
    print("Warning: openai package not installed. Install with: pip install openai")

from src.config import DASHSCOPE_API_KEY, BASE_URL, QWEN_MODEL_NAME


class OpenAICompatibleClient(ABC):
    """Abstract base class for OpenAI-compatible clients."""
    
    @abstractmethod
    def chat_completion(self, messages: List[Dict[str, str]], **kwargs) -> Dict[str, Any]:
        pass
    
    @abstractmethod
    def completion(self, prompt: str, **kwargs) -> Dict[str, Any]:
        pass


class QwenClient(OpenAICompatibleClient):
    """Qwen client using Alibaba Cloud DashScope OpenAI-compatible API."""
    
    def __init__(self, api_key: Optional[str] = None, base_url: Optional[str] = None, model_name: Optional[str] = None):
        self.api_key = api_key or DASHSCOPE_API_KEY
        self.base_url = base_url or BASE_URL
        self.model_name = model_name or QWEN_MODEL_NAME
        
        if not self.api_key:
            raise ValueError("DASHSCOPE_API_KEY is required")
        
        if OpenAI is None:
            raise ImportError("openai package is required. Install with: pip install openai")
        
        self.client = OpenAI(
            api_key=self.api_key,
            base_url=self.base_url
        )
    
    def chat_completion(self, messages: List[Dict[str, str]], **kwargs) -> Dict[str, Any]:
        """Make chat completion request."""
        try:
            # Use streaming mode to avoid enable_thinking parameter issue with non-streaming calls
            if 'stream' not in kwargs:
                kwargs['stream'] = True
            
            response = self.client.chat.completions.create(
                model=self.model_name,
                messages=messages,
                **kwargs
            )
            
            # Handle streaming response
            if kwargs.get('stream', False):
                # Collect streaming response into a single response
                full_content = ""
                print(f"🔄 Streaming response for model: {self.model_name}")
                for chunk in response:
                    if chunk.choices and chunk.choices[0].delta.content:
                        content = chunk.choices[0].delta.content
                        full_content += content
                        print(content, end='', flush=True)
                print()  # New line after response
                
                return {
                    "choices": [
                        {
                            "message": {
                                "content": full_content,
                                "role": "assistant"
                            }
                        }
                    ],
                    "usage": None  # Streaming responses don't include usage by default
                }
            else:
                # Non-streaming response
                print(f"✅ Non-streaming response for model: {self.model_name}")
                response_text = response.choices[0].message.content
                print(f"Response: {response_text}")
                
                return {
                    "choices": [
                        {
                            "message": {
                                "content": response_text,
                                "role": choice.message.role
                            }
                        }
                        for choice in response.choices
                    ],
                    "usage": {
                        "prompt_tokens": response.usage.prompt_tokens,
                        "completion_tokens": response.usage.completion_tokens,
                        "total_tokens": response.usage.total_tokens
                    } if response.usage else None
                }
        except Exception as e:
            print(f"Error in Qwen chat completion: {e}")
            raise
    
    def completion(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """Make legacy completion request."""
        messages = [{"role": "user", "content": prompt}]
        return self.chat_completion(messages, **kwargs)


class OpenAIStandardClient(OpenAICompatibleClient):
    """Standard OpenAI client."""
    
    def __init__(self, api_key: str, model_name: str = "gpt-3.5-turbo", base_url: Optional[str] = None):
        self.api_key = api_key
        self.model_name = model_name
        self.base_url = base_url
        
        if OpenAI is None:
            raise ImportError("openai package is required. Install with: pip install openai")
        
        self.client = OpenAI(
            api_key=self.api_key,
            base_url=self.base_url
        )
    
    def chat_completion(self, messages: List[Dict[str, str]], **kwargs) -> Dict[str, Any]:
        """Make chat completion request."""
        try:
            response = self.client.chat.completions.create(
                model=self.model_name,
                messages=messages,
                **kwargs
            )
            
            return {
                "choices": [
                    {
                        "message": {
                            "content": choice.message.content,
                            "role": choice.message.role
                        }
                    }
                    for choice in response.choices
                ],
                "usage": {
                    "prompt_tokens": response.usage.prompt_tokens,
                    "completion_tokens": response.usage.completion_tokens,
                    "total_tokens": response.usage.total_tokens
                } if response.usage else None
            }
            
        except Exception as e:
            print(f"Error in OpenAI chat completion: {e}")
            raise
    
    def completion(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """Make legacy completion request."""
        messages = [{"role": "user", "content": prompt}]
        return self.chat_completion(messages, **kwargs)


class ClientFactory:
    """Factory for creating appropriate OpenAI-compatible clients."""
    
    @staticmethod
    def create_client(
        provider: str = "qwen",
        api_key: Optional[str] = None,
        model_name: Optional[str] = None,
        base_url: Optional[str] = None
    ) -> OpenAICompatibleClient:
        """
        Create appropriate client based on provider.
        
        Args:
            provider: One of 'qwen', 'openai', 'azure'
            api_key: API key for the provider
            model_name: Model name to use
            base_url: Custom base URL (for local models or proxies)
        
        Returns:
            OpenAICompatibleClient instance
        """
        if provider == "qwen":
            return QwenClient(
                api_key=api_key,
                base_url=base_url,
                model_name=model_name
            )
        elif provider == "openai":
            return OpenAIStandardClient(
                api_key=api_key,
                base_url=base_url,
                model_name=model_name
            )
        else:
            raise ValueError(f"Unsupported provider: {provider}")


class RateLimitedClient:
    """Wrapper that adds rate limiting to any OpenAI-compatible client."""
    
    def __init__(self, client: OpenAICompatibleClient, requests_per_minute: int = 60):
        self.client = client
        self.requests_per_minute = requests_per_minute
        self.min_interval = 60.0 / requests_per_minute
        self.last_request_time = 0
    
    def _rate_limit(self):
        """Apply rate limiting."""
        now = time.time()
        time_since_last = now - self.last_request_time
        if time_since_last < self.min_interval:
            time.sleep(self.min_interval - time_since_last)
        self.last_request_time = time.time()
    
    def chat_completion(self, messages: List[Dict[str, str]], **kwargs) -> Dict[str, Any]:
        self._rate_limit()
        return self.client.chat_completion(messages, **kwargs)
    
    def completion(self, prompt: str, **kwargs) -> Dict[str, Any]:
        self._rate_limit()
        return self.client.completion(prompt, **kwargs)


# Convenience functions for common use cases
def get_default_client() -> OpenAICompatibleClient:
    """Get default client based on environment variables."""
    return ClientFactory.create_client(
        provider="qwen",
        api_key=DASHSCOPE_API_KEY,
        base_url=BASE_URL,
        model_name=QWEN_MODEL_NAME
    )


def extract_json_from_response(response_text: str) -> Dict[str, Any]:
    """Extract JSON from model response."""
    import re
    try:
        # Look for JSON in response
        json_match = re.search(r'\{[^}]*\}', response_text, re.DOTALL)
        if json_match:
            return json.loads(json_match.group())
        
        # If no JSON found, try to parse the whole response
        return json.loads(response_text)
    except (json.JSONDecodeError, AttributeError):
        return {}


if __name__ == "__main__":
    # Test the client
    try:
        client = get_default_client()
        result = client.chat_completion([
            {"role": "user", "content": "Hello, how are you?"}
        ], max_tokens=50)
        print("Client test successful:", result["choices"][0]["message"]["content"][:50] + "...")
    except Exception as e:
        print("Client test failed:", e)