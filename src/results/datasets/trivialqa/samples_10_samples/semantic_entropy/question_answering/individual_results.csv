task_id,question_id,question_text,expected_answer,category,semantic_entropy,uncertainty_score,uncertainty_level,confidence_interval_lower,confidence_interval_upper,num_clusters,max_entropy_possible,entropy_normalized,num_responses,avg_response_length,response_diversity,cluster_distribution,timestamp
trivialqa_semantic_entropy_e8cc2ce6_20250718_135325,tqa_000,"What is the type of this commit based on the commit message? Message: 'Create a new unstable workflow for periodic jobs (#98858)

And move ROCm distributed job there as it's very flaky in trunk at the moment.  Also move ROCm slow job to `slow` workflow as it should be.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/98858
Approved by: https://github.com/malfet, https://github.com/ZainRizvi' ",Continuous Integration,Software Development,3.6841837197791887,1.2280612399263962,high,2.947346975823351,4.421020463735026,15,3.0,1.2280612399263962,20,122.65,0.85,"{""cluster_1"": 0.05, ""cluster_2"": 0.15, ""cluster_3"": 0.2, ""cluster_4"": 0.05, ""cluster_5"": 0.05, ""cluster_6"": 0.05, ""cluster_7"": 0.05, ""cluster_8"": 0.05, ""cluster_9"": 0.05, ""cluster_10"": 0.05, ""cluster_11"": 0.05, ""cluster_12"": 0.05, ""cluster_13"": 0.05, ""cluster_14"": 0.05, ""cluster_15"": 0.05}",2025-07-18T13:53:25.299094
