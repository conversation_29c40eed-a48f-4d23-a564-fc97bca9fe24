#!/usr/bin/env python3
"""
Cache management utility for Qwen API responses.

This script provides tools to manage, inspect, and clear the response cache.
"""

import argparse
import sys
import os

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from response_cache import ResponseCache


def main():
    """Main cache management function."""
    parser = argparse.ArgumentParser(description="Manage Qwen API response cache")
    parser.add_argument("action", choices=["stats", "clear", "list", "resume-info"],
                       help="Action to perform")
    parser.add_argument("--prompt", type=str, help="Specific prompt to filter")
    parser.add_argument("--model", type=str, help="Specific model to filter")
    parser.add_argument("--samples", type=int, default=20, help="Number of samples for resume info")
    parser.add_argument("--cache-dir", type=str, default="cache", help="Cache directory")
    parser.add_argument("--cache-file", type=str, default="responses_cache.json", 
                       help="Cache file name")
    
    args = parser.parse_args()
    
    cache = ResponseCache(cache_dir=args.cache_dir, cache_file=args.cache_file)
    
    if args.action == "stats":
        stats = cache.get_cache_stats()
        print("CACHE STATISTICS")
        print("=" * 50)
        print(f"Total entries: {stats['total_entries']}")
        print(f"Cache file: {stats['cache_file']}")
        print(f"Unique prompts: {stats['prompts']}")
        
        if stats['models']:
            print("\nModels:")
            for model, count in stats['models'].items():
                print(f"  {model}: {count} responses")
    
    elif args.action == "list":
        cache_data = cache._cache
        if not cache_data:
            print("Cache is empty")
            return
            
        print("CACHED RESPONSES")
        print("=" * 50)
        
        for key, entry in cache_data.items():
            if args.prompt and args.prompt.lower() not in entry.get("prompt", "").lower():
                continue
            if args.model and args.model.lower() not in entry.get("model", "").lower():
                continue
                
            print(f"\nPrompt: {entry['prompt'][:100]}...")
            print(f"Model: {entry['model']}")
            print(f"Sample: {entry['sample_index'] + 1}")
            print(f"Response: {entry['response'][:100]}...")
            print(f"Timestamp: {entry['timestamp']}")
    
    elif args.action == "clear":
        print("CLEARING CACHE")
        print("=" * 50)
        
        if args.prompt or args.model:
            print(f"Clearing entries for:")
            if args.prompt:
                print(f"  Prompt: {args.prompt}")
            if args.model:
                print(f"  Model: {args.model}")
        else:
            print("Clearing entire cache")
        
        confirm = input("Are you sure? (y/N): ")
        if confirm.lower() == 'y':
            cache.clear_cache(args.prompt, args.model)
            print("Cache cleared successfully")
        else:
            print("Operation cancelled")
    
    elif args.action == "resume-info":
        if not args.prompt:
            print("Please provide --prompt for resume info")
            return
            
        model = args.model or "qwen3-32b"
        info = cache.get_resume_info(args.prompt, model, args.samples)
        
        print("RESUME INFORMATION")
        print("=" * 50)
        print(f"Prompt: {args.prompt}")
        print(f"Model: {model}")
        print(f"Cached responses: {info['cached_responses']}/{info['total_samples']}")
        print(f"Completion: {info['completion_percentage']:.1f}%")
        print(f"Next sample: {info['next_sample_index'] + 1}")
        print(f"Complete: {info['is_complete']}")


if __name__ == "__main__":
    main()