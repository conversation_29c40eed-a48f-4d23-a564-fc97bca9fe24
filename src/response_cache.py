"""
Response caching system for Qwen API responses.

This module provides caching functionality to store and retrieve responses
based on prompts, models, and sample indices. Supports resumption of
interrupted runs and deduplication of identical prompts.
"""

import os
import json
import hashlib
import time
from typing import Dict, List, Any, Optional
from pathlib import Path


class ResponseCache:
    """Cache system for storing and retrieving API responses."""
    
    def __init__(self, cache_dir: str = "cache", cache_file: str = "responses_cache.json"):
        """
        Initialize the response cache.
        
        Args:
            cache_dir: Directory to store cache files
            cache_file: Name of the cache file
        """
        self.cache_dir = Path(cache_dir)
        self.cache_file = self.cache_dir / cache_file
        self.cache_dir.mkdir(exist_ok=True)
        self._cache = self._load_cache()
        
    def _load_cache(self) -> Dict[str, Any]:
        """Load cache from disk."""
        if self.cache_file.exists():
            try:
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except (json.J<PERSON><PERSON>odeError, IOError):
                return {}
        return {}
    
    def _save_cache(self) -> None:
        """Save cache to disk."""
        try:
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(self._cache, f, ensure_ascii=False, indent=2)
        except IOError as e:
            print(f"Warning: Failed to save cache: {e}")
    
    def _generate_key(self, prompt: str, model: str, sample_index: int, **kwargs) -> str:
        """Generate a unique cache key for a request."""
        # Create a hash from prompt, model, sample index, and relevant kwargs
        key_data = {
            "prompt": prompt,
            "model": model,
            "sample_index": sample_index,
            "temperature": kwargs.get("temperature", 0.8),
            "max_tokens": kwargs.get("max_tokens", 200),
            "top_p": kwargs.get("top_p", 0.9)
        }
        
        key_string = json.dumps(key_data, sort_keys=True)
        return hashlib.md5(key_string.encode()).hexdigest()
    
    def get_response(self, prompt: str, model: str, sample_index: int, **kwargs) -> Optional[str]:
        """
        Get cached response for a specific prompt and sample.
        
        Args:
            prompt: The prompt text
            model: The model name
            sample_index: The sample number (0 to num_samples-1)
            **kwargs: Additional parameters for cache key generation
            
        Returns:
            Cached response if available, None otherwise
        """
        key = self._generate_key(prompt, model, sample_index, **kwargs)
        
        if key in self._cache:
            entry = self._cache[key]
            # Check if cache entry is still valid (not expired)
            if time.time() - entry.get("timestamp", 0) < 86400 * 7:  # 7 days
                print(f"📦 Using cached response for sample {sample_index + 1}")
                return entry["response"]
        
        return None
    
    def cache_response(self, prompt: str, model: str, sample_index: int, response: str, **kwargs) -> None:
        """
        Cache a response for a specific prompt and sample.
        
        Args:
            prompt: The prompt text
            model: The model name
            sample_index: The sample number
            response: The API response text
            **kwargs: Additional parameters for cache key generation
        """
        key = self._generate_key(prompt, model, sample_index, **kwargs)
        
        self._cache[key] = {
            "prompt": prompt,
            "model": model,
            "sample_index": sample_index,
            "response": response,
            "timestamp": time.time(),
            "parameters": {
                "temperature": kwargs.get("temperature", 0.8),
                "max_tokens": kwargs.get("max_tokens", 200),
                "top_p": kwargs.get("top_p", 0.9)
            }
        }
        
        self._save_cache()
    
    def get_responses_batch(self, prompt: str, model: str, num_samples: int, **kwargs) -> List[str]:
        """
        Get all cached responses for a complete batch.
        
        Args:
            prompt: The prompt text
            model: The model name
            num_samples: Total number of samples expected
            **kwargs: Additional parameters for cache key generation
            
        Returns:
            List of cached responses, empty list if incomplete
        """
        cached_responses = []
        
        for i in range(num_samples):
            response = self.get_response(prompt, model, i, **kwargs)
            if response is not None:
                cached_responses.append(response)
            else:
                # Incomplete batch, return empty
                return []
        
        return cached_responses
    
    def get_resume_info(self, prompt: str, model: str, num_samples: int, **kwargs) -> Dict[str, Any]:
        """
        Get information for resuming an interrupted run.
        
        Args:
            prompt: The prompt text
            model: The model name
            num_samples: Total number of samples expected
            **kwargs: Additional parameters for cache key generation
            
        Returns:
            Dictionary with resume information
        """
        cached_count = 0
        next_index = 0
        
        for i in range(num_samples):
            if self.get_response(prompt, model, i, **kwargs) is not None:
                cached_count += 1
                next_index = i + 1
            else:
                break
        
        return {
            "cached_responses": cached_count,
            "total_samples": num_samples,
            "completion_percentage": (cached_count / num_samples) * 100 if num_samples > 0 else 0,
            "next_sample_index": next_index,
            "is_complete": cached_count == num_samples
        }
    
    def clear_cache(self, prompt: str = None, model: str = None) -> None:
        """
        Clear cache entries, optionally filtered by prompt and/or model.
        
        Args:
            prompt: Clear entries for specific prompt (optional)
            model: Clear entries for specific model (optional)
        """
        if prompt is None and model is None:
            self._cache.clear()
        else:
            keys_to_remove = []
            for key, entry in self._cache.items():
                if prompt and entry.get("prompt") != prompt:
                    continue
                if model and entry.get("model") != model:
                    continue
                keys_to_remove.append(key)
            
            for key in keys_to_remove:
                del self._cache[key]
        
        self._save_cache()
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        if not self._cache:
            return {"total_entries": 0, "models": {}, "prompts": 0}
        
        models = {}
        prompts = set()
        
        for entry in self._cache.values():
            model = entry.get("model", "unknown")
            prompt = entry.get("prompt", "")
            
            models[model] = models.get(model, 0) + 1
            prompts.add(prompt)
        
        return {
            "total_entries": len(self._cache),
            "models": models,
            "prompts": len(prompts),
            "cache_file": str(self.cache_file)
        }