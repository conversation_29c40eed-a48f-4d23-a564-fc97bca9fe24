#!/usr/bin/env python3
"""Simple test for Qwen API with different approaches"""

import os
from openai import OpenAI

# Configuration
DASHSCOPE_API_KEY = os.getenv("DASHSCOPE_API_KEY", "sk-8a360a0f66fa416ba7ac5b428d39b0a5")
BASE_URL = "https://dashscope.aliyuncs.com/compatible-mode/v1"
MODEL_NAME = "qwen3-32b"

def test_streaming():
    """Test with streaming=True"""
    print("=== Testing with streaming=True ===")
    try:
        client = OpenAI(api_key=DASHSCOPE_API_KEY, base_url=BASE_URL)
        
        response = client.chat.completions.create(
            model=MODEL_NAME,
            messages=[{"role": "user", "content": "What is the capital of France?"}],
            stream=True,
            max_tokens=50
        )
        
        # Collect streaming response
        full_response = ""
        for chunk in response:
            if chunk.choices[0].delta.content:
                full_response += chunk.choices[0].delta.content
        
        print("✅ Streaming SUCCESS")
        print(f"Response: {full_response}")
        return True
    except Exception as e:
        print(f"❌ Streaming FAILED: {e}")
        return False

def test_non_streaming_with_param():
    """Test non-streaming with explicit enable_thinking=False"""
    print("\n=== Testing non-streaming with enable_thinking=False ===")
    try:
        client = OpenAI(api_key=DASHSCOPE_API_KEY, base_url=BASE_URL)
        
        response = client.chat.completions.create(
            model=MODEL_NAME,
            messages=[{"role": "user", "content": "What is the capital of France?"}],
            enable_thinking=False,
            stream=False,
            max_tokens=50
        )
        
        print("✅ Non-streaming with param SUCCESS")
        print(f"Response: {response.choices[0].message.content}")
        return True
    except Exception as e:
        print(f"❌ Non-streaming with param FAILED: {e}")
        return False

def test_non_streaming_without_param():
    """Test non-streaming without any enable_thinking parameter"""
    print("\n=== Testing non-streaming without enable_thinking ===")
    try:
        client = OpenAI(api_key=DASHSCOPE_API_KEY, base_url=BASE_URL)
        
        response = client.chat.completions.create(
            model=MODEL_NAME,
            messages=[{"role": "user", "content": "What is the capital of France?"}],
            stream=False,
            max_tokens=50
        )
        
        print("✅ Non-streaming without param SUCCESS")
        print(f"Response: {response.choices[0].message.content}")
        return True
    except Exception as e:
        print(f"❌ Non-streaming without param FAILED: {e}")
        return False

if __name__ == "__main__":
    print("Testing Qwen API formats...")
    
    test_streaming()
    test_non_streaming_with_param()
    test_non_streaming_without_param()