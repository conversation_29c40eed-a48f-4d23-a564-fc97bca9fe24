import pandas as pd
from pymongo import MongoClient
import argparse

def export_twitter_data_to_csv(mongo_host='localhost', mongo_port=27017, 
                              mongo_db='twitter_sentiment', mongo_collection='responses',
                              output_file='data/twitter_responses.csv',
                              limit_tweets=None):
    """
    从MongoDB导出Twitter响应数据到CSV文件
    """
    # 连接MongoDB
    client = MongoClient(host=mongo_host, port=mongo_port)
    db = client[mongo_db]
    collection = db[mongo_collection]
    
    # 构建查询pipeline
    pipeline = [
        {
            "$sort": {"tweet_index": 1, "prompt_type": 1}
        }
    ]
    
    # 如果指定了限制数量，则添加限制
    if limit_tweets:
        pipeline.extend([
            {
                "$group": {
                    "_id": "$tweet_index",
                    "docs": {"$push": "$$ROOT"}
                }
            },
            {
                "$limit": limit_tweets
            },
            {
                "$unwind": "$docs"
            },
            {
                "$replaceRoot": {"newRoot": "$docs"}
            },
            {
                "$sort": {"tweet_index": 1, "prompt_type": 1}
            }
        ])
    
    # 执行查询
    documents = list(collection.aggregate(pipeline))
    
    if not documents:
        print("No documents found in the collection")
        return
    
    # 转换为DataFrame
    df = pd.DataFrame(documents)
    
    # 选择需要的列
    columns_to_keep = ['tweet_index', 'prompt_type', 'text', 'validation', 'response_text', 'model_name', 'query_index']
    df = df[columns_to_keep]
    
    # 保存到CSV
    df.to_csv(output_file, index=False, encoding='utf-8-sig')
    
    print(f"Successfully exported {len(df)} records to {output_file}")
    print(f"Unique tweets: {df['tweet_index'].nunique()}")
    print(f"Prompt types: {df['prompt_type'].unique().tolist()}")
    
    # 显示每个tweet_index和prompt_type的响应数量
    summary = df.groupby(['tweet_index', 'prompt_type']).size().unstack(fill_value=0)
    print("\nResponses per tweet and prompt type:")
    print(summary.head(10))
    
    client.close()
    return df

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Export Twitter data from MongoDB to CSV")
    parser.add_argument('--mongo_host', type=str, default='localhost')
    parser.add_argument('--mongo_port', type=int, default=27017)
    parser.add_argument('--mongo_db', type=str, default='twitter_sentiment')
    parser.add_argument('--mongo_collection', type=str, default='responses')
    parser.add_argument('--output_file', type=str, default='data/twitter_responses.csv')
    parser.add_argument('--limit_tweets', type=int, default=5, help='Limit to first N tweets for testing')
    
    args = parser.parse_args()
    
    export_twitter_data_to_csv(
        mongo_host=args.mongo_host,
        mongo_port=args.mongo_port,
        mongo_db=args.mongo_db,
        mongo_collection=args.mongo_collection,
        output_file=args.output_file,
        limit_tweets=args.limit_tweets
    )