#!/usr/bin/env python3
"""
测试目录创建和缓存功能
"""

import os
import sys

# 添加当前目录到路径
sys.path.append('.')

try:
    from analyze_all_uq_methods import create_directories, OUTPUT_DIR, NLI_CACHE_DIR
    
    print("Testing directory creation...")
    create_directories()
    
    print(f"Checking if directories exist:")
    print(f"  {OUTPUT_DIR}: {'✓' if os.path.exists(OUTPUT_DIR) else '✗'}")
    print(f"  {NLI_CACHE_DIR}: {'✓' if os.path.exists(NLI_CACHE_DIR) else '✗'}")
    
    print("\nDirectories created successfully!")
    
except ImportError as e:
    print(f"Import error: {e}")
    print("Please make sure all required packages are installed.")
except Exception as e:
    print(f"Error: {e}")