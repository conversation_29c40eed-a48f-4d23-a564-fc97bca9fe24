import csv

input_path = "data/SemEval2017-task4-test.subtask-A.english.txt"
output_path = "data/SemEval2017-task4-test.subtask-A.english.csv"

with open(input_path, "r", encoding="utf-8") as infile, \
     open(output_path, "w", encoding="utf-8", newline="") as outfile:
    writer = csv.writer(outfile)
    writer.writerow(["id", "label", "text"])  # Write header

    for line in infile:
        parts = line.rstrip("\n").split("\t")
        if len(parts) == 3:
            writer.writerow(parts)
        else:
            print(f"Skipping malformed line: {line}")

print(f"Conversion complete! CSV saved to {output_path}") 