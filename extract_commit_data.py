import pandas as pd
import argparse

def extract_first_n_commits(input_file='data/commit_responses_20250723.csv', 
                           output_file='data/commit_responses_first5.csv',
                           n_commits=5):
    """
    从commit_responses CSV文件中提取前N个commit的所有responses
    """
    print(f"Reading data from {input_file}...")
    
    # 读取CSV文件，但由于文件很大，我们先读取一小部分来获取唯一的commit_sha
    chunk_size = 10000
    unique_commits = set()
    
    # 逐块读取文件来获取唯一的commit_sha
    for chunk in pd.read_csv(input_file, chunksize=chunk_size):
        unique_commits.update(chunk['commit_sha'].unique())
        # 如果已经找到足够的commits，就停止
        if len(unique_commits) >= n_commits * 2:  # 预留一些余量
            break
    
    # 取前N个commit SHA
    first_n_commits = list(unique_commits)[:n_commits]
    print(f"Selected first {n_commits} commits: {first_n_commits}")
    
    # 现在读取完整文件并过滤出这些commits的数据
    print("Reading full data and filtering...")
    df = pd.read_csv(input_file)
    
    # 过滤出前N个commit的数据
    filtered_df = df[df['commit_sha'].isin(first_n_commits)].copy()
    
    # 按commit_sha和prompt_type排序
    filtered_df = filtered_df.sort_values(['commit_sha', 'prompt_type', 'query_index'])
    
    # 保存到新文件
    filtered_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    
    print(f"Successfully extracted {len(filtered_df)} records to {output_file}")
    print(f"Commits included: {filtered_df['commit_sha'].unique().tolist()}")
    print(f"Prompt types: {filtered_df['prompt_type'].unique().tolist()}")
    
    # 显示每个commit和prompt_type的响应数量
    summary = filtered_df.groupby(['commit_sha', 'prompt_type']).size().unstack(fill_value=0)
    print("\nResponses per commit and prompt type:")
    print(summary)
    
    return filtered_df

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Extract first N commits from commit responses CSV")
    parser.add_argument('--input_file', type=str, default='data/commit_responses_20250723.csv')
    parser.add_argument('--output_file', type=str, default='data/commit_responses_first5.csv')
    parser.add_argument('--n_commits', type=int, default=5, help='Number of commits to extract')
    
    args = parser.parse_args()
    
    extract_first_n_commits(
        input_file=args.input_file,
        output_file=args.output_file,
        n_commits=args.n_commits
    )